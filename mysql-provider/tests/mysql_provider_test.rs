use mysql_provider::{MySqlConfig, MySqlProvider, MySqlProviderImpl};
use sqlx::FromRow;
use std::env;

// 示例实体
#[derive(Debug, FromRow)]
pub struct User {
    pub id: i64,
    pub username: String,
    pub email: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

// 辅助函数：从环境变量获取数据库配置，若不存在则使用默认值
fn get_test_config() -> MySqlConfig {
    MySqlConfig {
        host: env::var("TEST_MYSQL_HOST").unwrap_or_else(|_| "localhost".to_string()),
        port: env::var("TEST_MYSQL_PORT")
            .ok()
            .and_then(|p| p.parse::<u16>().ok())
            .unwrap_or(3306),
        username: env::var("TEST_MYSQL_USER").unwrap_or_else(|_| "root".to_string()),
        password: env::var("TEST_MYSQL_PASSWORD").unwrap_or_else(|_| "123456".to_string()),
        database: env::var("TEST_MYSQL_DB").unwrap_or_else(|_| "test".to_string()),
        ..MySqlConfig::default()
    }
}

// 测试连接到MySQL数据库
#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_connection() {
    let config = get_test_config();
    let provider = MySqlProviderImpl::new(config).await;
    assert!(provider.is_ok(), "无法连接到MySQL数据库");
}

// 测试用户表创建和查询（需要有测试数据库）
#[cfg(feature = "run-tests")]
#[tokio::test] // 默认忽略此测试，因为它需要实际的数据库连接
async fn test_user_crud() {
    let config = get_test_config();
    let provider = MySqlProviderImpl::new(config).await.unwrap();

    // 创建测试表
    let create_table_query = r#"
    CREATE TABLE IF NOT EXISTS users (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    "#;

    provider.execute(create_table_query).await.unwrap();

    // 清理现有数据
    provider.execute("DELETE FROM users").await.unwrap();

    // 插入测试用户
    let insert_query = r#"
    INSERT INTO users (username, email) VALUES
    ('test_user1', '<EMAIL>'),
    ('test_user2', '<EMAIL>')
    "#;

    let rows_affected = provider.execute(insert_query).await.unwrap();
    assert_eq!(rows_affected, 2, "应该插入两条记录");

    // 查询用户
    let users: Vec<User> = provider.query("SELECT * FROM users ORDER BY id").await.unwrap();

    assert_eq!(users.len(), 2, "应该查询到两条用户记录");
    assert_eq!(users[0].username, "test_user1");
    assert_eq!(users[0].email, Some("<EMAIL>".to_string()));
    assert_eq!(users[1].username, "test_user2");
    assert_eq!(users[1].email, Some("<EMAIL>".to_string()));
    assert_eq!(users[0].created_at, users[1].created_at);

    // 带参数查询
    let filtered_users: Vec<User> = provider
        .query_with_param("SELECT * FROM users WHERE username = ?", "test_user1")
        .await
        .unwrap();
    assert_eq!(filtered_users.len(), 1);
    assert_eq!(filtered_users[0].username, "test_user1");

    // 更新用户
    provider
        .execute_with_param("UPDATE users SET email = '<EMAIL>' WHERE username = ?", "test_user1")
        .await
        .unwrap();

    // 验证更新
    let updated_users: Vec<User> = provider
        .query("SELECT * FROM users WHERE username = 'test_user1'")
        .await
        .unwrap();
    assert_eq!(updated_users[0].email, Some("<EMAIL>".to_string()));

    // 删除表
    provider.execute("DROP TABLE users").await.unwrap();
}

// 测试使用自定义结构体查询结果
#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_custom_struct_mapping() {
    #[derive(Debug, FromRow)]
    struct TestData {
        value: i32,
        text: String,
    }

    let config = get_test_config();
    let provider = MySqlProviderImpl::new(config).await.unwrap();

    // 简单查询返回自定义结构体
    let data: Vec<TestData> = provider.query("SELECT 42 as value, 'test' as text").await.unwrap();

    assert_eq!(data.len(), 1);
    assert_eq!(data[0].value, 42);
    assert_eq!(data[0].text, "test");
}

// 测试批量查询功能
#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_batch_execution() {
    let config = get_test_config();
    let provider = MySqlProviderImpl::new(config).await.unwrap();

    // 创建测试表
    provider
        .execute("CREATE TABLE IF NOT EXISTS test_batch (id INT PRIMARY KEY, value TEXT)")
        .await
        .unwrap();

    // 清理现有数据
    provider.execute("DELETE FROM test_batch").await.unwrap();

    // 批量执行查询
    let queries = vec![
        "INSERT INTO test_batch (id, value) VALUES (1, 'one')".to_string(),
        "INSERT INTO test_batch (id, value) VALUES (2, 'two')".to_string(),
        "INSERT INTO test_batch (id, value) VALUES (3, 'three')".to_string(),
    ];

    provider.execute_batch(&queries).await.unwrap();

    // 验证结果
    #[derive(Debug, FromRow)]
    struct TestRecord {
        id: i32,
        value: String,
    }

    let records: Vec<TestRecord> = provider.query("SELECT * FROM test_batch ORDER BY id").await.unwrap();

    assert_eq!(records.len(), 3);
    assert_eq!(records[0].id, 1);
    assert_eq!(records[0].value, "one");
    assert_eq!(records[1].id, 2);
    assert_eq!(records[1].value, "two");
    assert_eq!(records[2].id, 3);
    assert_eq!(records[2].value, "three");

    // 清理表
    provider.execute("DROP TABLE test_batch").await.unwrap();
}

// 测试事务功能
#[cfg(feature = "run-tests")]
#[tokio::test]
async fn test_transaction() {
    let config = get_test_config();
    let provider = MySqlProviderImpl::new(config).await.unwrap();
    let pool = provider.get_pool();

    // 创建测试表
    provider
        .execute("CREATE TABLE IF NOT EXISTS test_transaction (id INT PRIMARY KEY, value TEXT)")
        .await
        .unwrap();

    // 清理现有数据
    provider.execute("DELETE FROM test_transaction").await.unwrap();

    // 测试事务提交
    {
        let mut tx = pool.begin().await.unwrap();

        sqlx::query("INSERT INTO test_transaction (id, value) VALUES (1, 'one')")
            .execute(&mut *tx)
            .await
            .unwrap();

        sqlx::query("INSERT INTO test_transaction (id, value) VALUES (2, 'two')")
            .execute(&mut *tx)
            .await
            .unwrap();

        // 提交事务
        tx.commit().await.unwrap();
    }

    // 验证提交的数据存在
    #[derive(Debug, FromRow)]
    struct TestRecord {
        id: i32,
        value: String,
    }

    let records: Vec<TestRecord> = provider.query("SELECT * FROM test_transaction ORDER BY id").await.unwrap();
    assert_eq!(records.len(), 2);
    assert_eq!(records[0].id, 1);
    assert_eq!(records[0].value, "one");
    assert_eq!(records[1].id, 2);
    assert_eq!(records[1].value, "two");

    // 测试事务回滚
    {
        let mut tx = pool.begin().await.unwrap();

        sqlx::query("INSERT INTO test_transaction (id, value) VALUES (3, 'three')")
            .execute(&mut *tx)
            .await
            .unwrap();

        // 回滚事务（不提交）
        tx.rollback().await.unwrap();
    }

    // 验证回滚后数据不存在
    let records_after_rollback: Vec<TestRecord> =
        provider.query("SELECT * FROM test_transaction ORDER BY id").await.unwrap();
    assert_eq!(records_after_rollback.len(), 2); // 仍然只有2条记录
    assert!(records_after_rollback.iter().all(|r| r.id != 3)); // 不存在id=3的记录

    // 清理表
    provider.execute("DROP TABLE test_transaction").await.unwrap();
}
