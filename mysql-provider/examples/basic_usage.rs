use env_logger;
use log::{error, info, LevelFilter};
use mysql_provider::{MySqlConfig, MySqlProvider, MySqlProviderError, MySqlProviderImpl};
use rust_decimal::Decimal;
use sqlx::FromRow;
use std::env;
use std::time::Duration;

// 自定义数据结构
#[derive(Debug, FromRow)]
struct Product {
    id: i64,
    name: String,
    price: Decimal,
    description: Option<String>,
}

#[tokio::main]
async fn main() {
    // 初始化日志，带有时间戳
    env_logger::Builder::from_default_env()
        .format_timestamp_millis()
        .format_module_path(false)
        .filter_level(LevelFilter::Info)
        .init();

    // 从环境变量或配置文件获取数据库配置
    let config = MySqlConfig {
        host: env::var("MYSQL_HOST").unwrap_or_else(|_| "localhost".to_string()),
        port: env::var("MYSQL_PORT").ok().and_then(|p| p.parse::<u16>().ok()).unwrap_or(3306),
        username: env::var("MYSQL_USER").unwrap_or_else(|_| "root".to_string()),
        password: env::var("MYSQL_PASSWORD").unwrap_or_else(|_| "123456".to_string()),
        database: env::var("MYSQL_DB").unwrap_or_else(|_| "test".to_string()),
        max_connections: 5,
        min_connections: 1,
        connection_timeout: Duration::from_secs(5),
        max_lifetime: Duration::from_secs(30 * 60),
        idle_timeout: Duration::from_secs(10 * 60),
    };

    info!("尝试连接到MySQL数据库: {}:{}/{}", config.host, config.port, config.database);

    // 使用match处理错误，确保显示自定义错误消息
    match run_example(config).await {
        Ok(_) => info!("示例执行完成!"),
        Err(e) => error!("{}", e),
    }
}

// 将主要逻辑分离到单独的函数中以便错误处理
async fn run_example(config: MySqlConfig) -> Result<(), MySqlProviderError> {
    // 创建MySQL提供程序
    let provider = MySqlProviderImpl::new(config).await?;
    info!("成功连接到数据库!");

    // 示例: 创建产品表
    let create_table_query = r#"
    CREATE TABLE IF NOT EXISTS products (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    "#;

    provider.execute(create_table_query).await?;
    info!("产品表已创建或已存在");

    // 清理任何现有数据
    provider.execute("DELETE FROM products").await?;

    // 插入一些产品数据
    let insert_query = r#"
    INSERT INTO products (name, price, description) VALUES
    ('笔记本电脑', 5999.99, '高性能笔记本电脑'),
    ('手机', 2999.50, '智能手机'),
    ('耳机', 299.00, '无线蓝牙耳机')
    "#;

    let rows_affected = provider.execute(insert_query).await?;
    info!("已插入 {} 条产品记录", rows_affected);

    // 查询所有产品
    let products: Vec<Product> = provider
        .query("SELECT id, name, price, description FROM products ORDER BY id")
        .await?;

    // 显示查询结果
    info!("产品列表:");
    for product in &products {
        info!(
            "ID: {}, 名称: {}, 价格: {}, 描述: {}",
            product.id,
            product.name,
            product.price,
            product.description.as_deref().unwrap_or("无描述")
        );
    }

    // 使用参数化查询查找特定产品
    let laptops: Vec<Product> = provider
        .query_with_param("SELECT id, name, price, description FROM products WHERE name LIKE ?", "%笔记本%")
        .await?;

    info!("笔记本电脑产品:");
    for laptop in &laptops {
        info!(
            "ID: {}, 名称: {}, 价格: {}, 描述: {}",
            laptop.id,
            laptop.name,
            laptop.price,
            laptop.description.as_deref().unwrap_or("无描述")
        );
    }

    // 更新产品价格
    provider
        .execute("UPDATE products SET price = 6499.99 WHERE name = '笔记本电脑'")
        .await?;
    info!("已更新笔记本电脑价格");

    // 验证更新
    let updated_laptops: Vec<Product> = provider
        .query("SELECT id, name, price, description FROM products WHERE name = '笔记本电脑'")
        .await?;

    info!("更新后的笔记本电脑产品:");
    for laptop in &updated_laptops {
        info!(
            "ID: {}, 名称: {}, 价格: {}, 描述: {}",
            laptop.id,
            laptop.name,
            laptop.price,
            laptop.description.as_deref().unwrap_or("无描述")
        );
    }

    // 使用批量查询添加多条记录
    let queries = vec![
        "INSERT INTO products (name, price, description) VALUES ('显示器', 1299.00, '27寸4K显示器')".to_string(),
        "INSERT INTO products (name, price, description) VALUES ('键盘', 399.00, '机械键盘')".to_string(),
    ];

    provider.execute_batch(&queries).await?;
    info!("通过批量操作添加了2条新产品");

    // 查询所有产品以验证批量操作
    let all_products: Vec<Product> = provider
        .query("SELECT id, name, price, description FROM products ORDER BY id")
        .await?;

    info!("所有产品列表:");
    for all_product in &all_products {
        info!(
            "ID: {}, 名称: {}, 价格: {}, 描述: {}",
            all_product.id,
            all_product.name,
            all_product.price,
            all_product.description.as_deref().unwrap_or("无描述")
        );
    }

    Ok(())
}
