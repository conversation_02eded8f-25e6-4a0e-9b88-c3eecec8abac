[package]
name = "mysql-provider"
version = "0.1.0"
edition = "2021"

[dependencies]
sqlx = { workspace = true }
tokio = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
chrono = { workspace = true }
thiserror = { workspace = true }
futures = { workspace = true }
rust_decimal = { workspace = true }
log = { workspace = true }
env_logger.workspace = true

[features]
run-tests = []
