use serde::Deserialize;

/// Task execution parameters corresponding to the JSON parameters in Scala main method
#[derive(Debug, Clone, Deserialize)]
pub struct CpTaskParams {
    #[serde(rename = "customer")]
    pub customer: String,
    #[serde(rename = "subCustomer")]
    pub sub_customer: String,
    #[serde(rename = "factory")]
    pub factory: String,
    #[serde(rename = "factorySite")]
    pub factory_site: String,
    #[serde(rename = "testArea")]
    pub test_area: String,
    #[serde(rename = "lotId")]
    pub lot_id: String,
    #[serde(rename = "waferNo")]
    pub wafer_no: String,
    #[serde(rename = "lotType")]
    pub lot_type: String,
    #[serde(rename = "deviceId")]
    pub device_id: String,
    #[serde(rename = "testStage")]
    pub test_stage: String,
    #[serde(rename = "executeMode")]
    pub execute_mode: String,
    #[serde(rename = "fileCategory")]
    pub file_category: String,
    #[serde(rename = "ckSinkType")]
    pub ck_sink_type: String,
    #[serde(rename = "hdfsResultPartition")]
    pub test_item_detail_result_partition: String,
    #[serde(rename = "dataVersion")]
    pub data_version: String,
    #[serde(rename = "calculateYmsTestItem")]
    pub calculate_yms_test_item: String,
}

impl CpTaskParams {
    pub fn new(params: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(params)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_new() -> Result<(), serde_json::Error> {
        let params = CpTaskParams::new(
            r#"{"factory":"LEADYO",
            "executeMode":"AUTO",
            "testArea":"CP",
            "ckSinkType":"JDBC",
            "subCustomer":"YEESTOR",
            "dataVersion":"1751981589283",
            "calculateYmsTestItem":"1",
            "hdfsResultPartition":"17",
            "submitMode":"SINGLE",
            "lotId":"ENF083",
            "deviceId":"YS8293ENAB",
            "version":"1752016677052",
            "dieCount":"14077",
            "command":"\"\"",
            "factorySite":"LEADYO",
            "waferNo":"25",
            "newDataFlag":"true",
            "testStage":"CP1",
            "cores":"12",
            "fileCategory":"STDF",
            "lotType":"PRODUCTION",
            "customer":"YEESTOR"}
            "#,
        )?;
        assert_eq!(params.customer, "YEESTOR");
        assert_eq!(params.sub_customer, "YEESTOR");
        assert_eq!(params.factory, "LEADYO");
        assert_eq!(params.factory_site, "LEADYO");
        assert_eq!(params.test_area, "CP");
        assert_eq!(params.lot_id, "ENF083");
        assert_eq!(params.wafer_no, "25");
        assert_eq!(params.lot_type, "PRODUCTION");
        assert_eq!(params.device_id, "YS8293ENAB");
        assert_eq!(params.test_stage, "CP1");
        assert_eq!(params.execute_mode, "AUTO");
        assert_eq!(params.file_category, "STDF");
        assert_eq!(params.ck_sink_type, "JDBC");
        assert_eq!(params.test_item_detail_result_partition, "17");
        assert_eq!(params.data_version, "1751981589283");
        assert_eq!(params.calculate_yms_test_item, "1");
        Ok(())
    }
}
