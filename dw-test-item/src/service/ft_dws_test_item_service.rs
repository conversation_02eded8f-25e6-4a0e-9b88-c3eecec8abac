//! FT DWS测试项服务实现
//!
//! 实现FT工艺的DWS层测试项数据处理逻辑

use log::info;
use anyhow::Result;
use std::collections::HashMap;
use chrono;

// 从common模块导入所需结构体
use common::dto::dwd::die_detail_row::DieDetailRow;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dws::dws_service::{DwsService, DwsSubDieDetail, DwsSubTestItemDetail};
use common::dws::table::distributed::bin_test_item_index_service::BinTestItemIndexService;
use common::dws::table::distributed::site_test_item_index_service::SiteTestItemIndexService;
use common::dws::table::distributed::test_item_index_service::TestItemIndexService;
use common::ck::ck_sink::{CkSink, SinkHandler};
use common::utils::path;
use common::utils::ck_util::CkUtil;
use common::model::key::wafer_key::WaferKey;
use ck_provider::{CkConfig, CkProvider, CkProviderImpl};
use parquet_provider::parquet_provider::write_parquet;
use crate::config::DwTestItemConfig;

// 从common::dws::model导入索引结构体
use common::dws::model::{BinTestItemIndex, SiteTestItemIndex, TestItemIndex};



/// FT DWS测试项服务
pub struct FtDwsTestItemService {
    // DWS服务实例
    dws_service: DwsService,
    // 配置
    properties: DwTestItemConfig,
}

impl FtDwsTestItemService {
    /// 创建新的FT DWS测试项服务实例
    pub fn new() -> Result<Self> {
        let properties = DwTestItemConfig::get_config()?;
        
        Ok(Self {
            dws_service: DwsService,
            properties,
        })
    }
    

    
    /// 计算DWS层测试项数据
    pub async fn calculate(
        &self,
        die_detail: Vec<DieDetailRow>,
        test_item_detail: Vec<SubTestItemDetail>,
        wafer_key: &WaferKey,
        test_area: &str,
    ) -> Result<()> {
        info!("Calculating FT DWS test item for lot: {}", wafer_key.lot_id);
        
        // 构建DWS子Die详情 - FT工艺特殊处理，需要处理retest_bin_num
        let _dws_die_detail: Vec<DwsSubDieDetail> = die_detail
            .iter()
            .map(|item| DwsService::build_dws_sub_die_detail(item))
            .collect();
        
        // 构建DWS子测试项详情 - FT工艺只过滤TEST_VALUE
        let dws_test_item_detail: Vec<DwsSubTestItemDetail> = test_item_detail
            .iter()
            .filter(|item| {
                item.TEST_VALUE.is_some() && 
                item.TEST_VALUE.unwrap().is_finite()
            })
            .map(|item| DwsService::build_dws_sub_test_item_detail(item))
            .collect();

        // 计算Bin测试项索引
        self.calculate_bin_test_item_index(&dws_test_item_detail, test_area, wafer_key).await?;
        
        // 计算站点测试项索引
        self.calculate_site_test_item_index(&dws_test_item_detail, test_area, wafer_key).await?;
        
        // 计算测试项索引
        self.calculate_test_item_index(&dws_test_item_detail, test_area, wafer_key).await?;
        
        info!("FT DWS calculation completed for lot: {}", wafer_key.lot_id);
        Ok(())
    }
    
    /// 计算Bin测试项索引
    async fn calculate_bin_test_item_index(
        &self, 
        dws_test_item_detail: &[DwsSubTestItemDetail], 
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<()> {
        info!("Calculating bin test item index for {} items", dws_test_item_detail.len());
        
        let bin_test_item_service = BinTestItemIndexService::new(test_area.to_string());
        // FT工艺需要retest_bin_num_map参数，根据Scala代码构建
        let retest_bin_num_map = HashMap::new(); // 简化处理，实际项目中需要构建这个映射
        let bin_test_item_indices = bin_test_item_service.calculate(dws_test_item_detail, Some(&retest_bin_num_map));
        
        info!("Generated {} bin test item indices", bin_test_item_indices.len());
        
        if !bin_test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_bin_test_item_index_to_hdfs(&bin_test_item_indices, test_area, wafer_key).await?;
            
            // 写入ClickHouse
            self.write_bin_test_item_index_to_clickhouse(&bin_test_item_indices, wafer_key, test_area).await?;
        }
        
        Ok(())
    }
    
    /// 计算站点测试项索引
    async fn calculate_site_test_item_index(
        &self, 
        dws_test_item_detail: &[DwsSubTestItemDetail],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<()> {
        info!("Calculating site test item index for {} items", dws_test_item_detail.len());
        
        let site_test_item_service = SiteTestItemIndexService::new(test_area.to_string());
        let site_test_item_indices = site_test_item_service.calculate(dws_test_item_detail);
        
        info!("Generated {} site test item indices", site_test_item_indices.len());
        
        if !site_test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_site_test_item_index_to_hdfs(&site_test_item_indices, test_area, wafer_key).await?;
            
            // 写入ClickHouse
            self.write_site_test_item_index_to_clickhouse(&site_test_item_indices).await?;
        }
        
        Ok(())
    }
    
    /// 计算测试项索引
    async fn calculate_test_item_index(
        &self, 
        dws_test_item_detail: &[DwsSubTestItemDetail],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<()> {
        info!("Calculating test item index for {} items", dws_test_item_detail.len());
        
        let test_item_service = TestItemIndexService::new(test_area.to_string());
        let test_item_indices = test_item_service.calculate(dws_test_item_detail);
        
        info!("Generated {} test item indices", test_item_indices.len());
        
        if !test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_test_item_index_to_hdfs(&test_item_indices, test_area, wafer_key).await?;
            
            // 写入ClickHouse
            self.write_test_item_index_to_clickhouse(&test_item_indices, wafer_key, test_area).await?;
        }
        
        Ok(())
    }

    // ========== HDFS写入方法 ==========
    
    /// 写入BinTestItemIndex到HDFS
    async fn write_bin_test_item_index_to_hdfs(
        &self,
        data: &[BinTestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<()> {
        // FT工艺使用lot路径，不使用wafer_no
        let table_path = path::get_lot_path(
            &self.properties.ft_dws_result_dir_template,
            "dws_bin_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );
        
        info!("写入BinTestItemIndex parquet文件到路径: {}", table_path);
        
        // 确保目录存在
        if let Some(parent) = std::path::Path::new(&table_path).parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| anyhow::anyhow!("创建目录失败: {}", e))?;
        }
        
        // 写入parquet文件
        write_parquet(&table_path, "bin_test_item_index.parquet", &data.to_vec(), None);
        info!("成功写入BinTestItemIndex parquet文件到: {}/bin_test_item_index.parquet", table_path);
        
        Ok(())
    }
    
    /// 写入SiteTestItemIndex到HDFS
    async fn write_site_test_item_index_to_hdfs(
        &self,
        data: &[SiteTestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<()> {
        // FT工艺使用lot路径，不使用wafer_no
        let table_path = path::get_lot_path(
            &self.properties.ft_dws_result_dir_template,
            "dws_site_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );
        
        info!("写入SiteTestItemIndex parquet文件到路径: {}", table_path);
        
        // 确保目录存在
        if let Some(parent) = std::path::Path::new(&table_path).parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| anyhow::anyhow!("创建目录失败: {}", e))?;
        }
        
        // 写入parquet文件
        write_parquet(&table_path, "site_test_item_index.parquet", &data.to_vec(), None);
        info!("成功写入SiteTestItemIndex parquet文件到: {}/site_test_item_index.parquet", table_path);
        
        Ok(())
    }
    
    /// 写入TestItemIndex到HDFS
    async fn write_test_item_index_to_hdfs(
        &self,
        data: &[TestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<()> {
        // FT工艺使用lot路径，不使用wafer_no
        let table_path = path::get_lot_path(
            &self.properties.ft_dws_result_dir_template,
            "dws_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );
        
        info!("写入TestItemIndex parquet文件到路径: {}", table_path);
        
        // 确保目录存在
        if let Some(parent) = std::path::Path::new(&table_path).parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| anyhow::anyhow!("创建目录失败: {}", e))?;
        }
        
        // 写入parquet文件
        write_parquet(&table_path, "test_item_index.parquet", &data.to_vec(), None);
        info!("成功写入TestItemIndex parquet文件到: {}/test_item_index.parquet", table_path);
        
        Ok(())
    }
    
    // ========== ClickHouse写入方法 ==========
    
    /// 写入BinTestItemIndex到ClickHouse
    async fn write_bin_test_item_index_to_clickhouse(
        &self,
        data: &[BinTestItemIndex],
        wafer_key: &WaferKey,
        test_area: &str,
    ) -> Result<()> {
        use common::dws::sink::BinTestItemIndexHandler;
        
        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let handler = BinTestItemIndexHandler::new(self.properties.dws_db_name.clone());
        
        info!("开始写入BinTestItemIndex到ClickHouse，数据量: {}", data.len());
        
        // BinTestItemIndex使用5参数版本（包含deviceId）
        let partition_expr = CkUtil::get_dws_replace_merge_tree_partition_with_device(
            &wafer_key.customer,
            test_area,
            &wafer_key.factory,
            &wafer_key.sub_customer,
            &wafer_key.device_id,
        );
        
        CkSink::write_to_ck_with_partition_from_config(
            data,
            &self.properties.index_num_partition,
            ck_config,
            &handler,
            &partition_expr,
        ).await.map_err(|e| anyhow::anyhow!("ClickHouse写入失败: {}", e))?;
        
        info!("成功写入BinTestItemIndex到ClickHouse");
        Ok(())
    }
    
    /// 写入SiteTestItemIndex到ClickHouse
    /// 注意：SiteTestItemIndex在Scala代码中使用writeToCk方法，不需要分区表达式
    async fn write_site_test_item_index_to_clickhouse(
        &self,
        data: &[SiteTestItemIndex],
    ) -> Result<()> {
        use common::dws::sink::SiteTestItemIndexHandler;
        
        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let handler = SiteTestItemIndexHandler::new_local(self.properties.dws_db_name.clone());
        
        info!("开始写入SiteTestItemIndex到ClickHouse，数据量: {}", data.len());
        
        // 对应Scala中的writeToCk方法，不使用分区表达式
        let num_partitions = self.properties.index_num_partition.parse::<usize>()
            .map_err(|e| anyhow::anyhow!("无效的分区数: {}", e))?;
        
        CkSink::write_to_ck(
            data,
            num_partitions,
            &ck_config,
            &handler,
            false, // is_batch
        ).await.map_err(|e| anyhow::anyhow!("ClickHouse写入失败: {}", e))?;
        
        info!("成功写入SiteTestItemIndex到ClickHouse");
        Ok(())
    }
    
    /// 写入TestItemIndex到ClickHouse
    async fn write_test_item_index_to_clickhouse(
        &self,
        data: &[TestItemIndex],
        wafer_key: &WaferKey,
        test_area: &str,
    ) -> Result<()> {
        use common::dws::sink::TestItemIndexHandler;
        
        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let handler = TestItemIndexHandler::new(self.properties.dws_db_name.clone());
        
        info!("开始写入TestItemIndex到ClickHouse，数据量: {}", data.len());
        
        // TestItemIndex使用4参数版本（不包含deviceId）
        let partition_expr = CkUtil::get_dws_replace_merge_tree_partition(
            &wafer_key.customer,
            test_area,
            &wafer_key.factory,
            &wafer_key.sub_customer,
        );
        
        CkSink::write_to_ck_with_partition_from_config(
            data,
            &self.properties.index_num_partition,
            ck_config,
            &handler,
            &partition_expr,
        ).await.map_err(|e| anyhow::anyhow!("ClickHouse写入失败: {}", e))?;
        
        info!("成功写入TestItemIndex到ClickHouse");
        Ok(())
    }
}