use color_eyre::{eyre::WrapErr, Result};
use dw_test_item::error::DatawareError;

fn main() -> Result<()> {
    // 安装 eyre 的钩子，这是实现漂亮打印和 backtrace 捕获的关键
    color_eyre::install()?;

    // 运行你的主应用逻辑
    println!("Starting application...");
    if let Err(e) = run_application_logic() {
        // 这里的 `{:?}` 会触发 eyre 的详细报告格式，完全等价于 e.printStackTrace()
        eprintln!("\n--- An error occurred! ---");
        eprintln!("{:?}", e);
        eprintln!("--------------------------");
        std::process::exit(1);
    }

    println!("Application finished successfully.");
    Ok(())
}

/// 模拟你的应用主逻辑
fn run_application_logic() -> Result<()> {
    // 使用 `wrap_err` 或 `context` 来添加上下文信息，这会构建出 "Caused by:" 链
    process_layer_one().wrap_err("Failed during top-level application logic")?;
    Ok(())
}

#[track_caller]
fn process_layer_one() -> Result<()> {
    process_layer_two().wrap_err("Failed to process layer one")?;
    Ok(())
}

fn process_layer_two() -> Result<()> {
    // 模拟一个最底层的错误
    Err(DatawareError::TombstoneFailed("exit code 127".to_string()).into()) // .into() 将我们的 DatawareError 转换为 eyre::Report
}
