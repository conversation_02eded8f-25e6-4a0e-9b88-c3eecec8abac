// mod config;

use anyhow::Result;
use dw_test_item::config::DwTestItemConfig;
use mysql_provider::{MySqlConfig, MySqlProvider, MySqlProviderImpl};
use std::time::Duration;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[tokio::main]
async fn main() -> Result<()> {
    // 设置日志
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .with_line_number(true)
        .init();

    // 读取配置文件
    let config = DwTestItemConfig::get_config()?;
    println!("config: {:?}", config);
    // 从JDBC URL解析主机和端口
    // 格式: *******************************
    let url_parts: Vec<&str> = config.address.split("://").collect();
    let host_port_db: Vec<&str> = url_parts[1].split('/').collect();
    let host_port: Vec<&str> = host_port_db[0].split(':').collect();

    let host = host_port.get(0).unwrap().to_string();
    let port = host_port.get(1).unwrap().parse::<u16>().unwrap();
    let database = config.onedatadbname.clone();

    // 创建MySQL配置
    let mysql_config = MySqlConfig {
        host: host.clone(),
        port,
        username: config.username.clone(),
        password: config.password.clone(),
        database: database.clone(),
        max_connections: 10,
        min_connections: 1,
        connection_timeout: Duration::from_secs(5),
        max_lifetime: Duration::from_secs(30 * 60),
        idle_timeout: Duration::from_secs(10 * 60),
    };

    // 使用配置创建MySQL提供者
    let mysql_provider = MySqlProviderImpl::new(mysql_config).await?;

    println!("成功读取配置并创建MySQL提供者");
    println!("MySQL地址: {}:{}", host, port);
    println!("数据库名称: {}", database);

    // 在这里添加更多的业务逻辑...
    // 例如执行查询
    let count = mysql_provider
        .count("select count(*) from dw_module_scheduled_control_switch")
        .await?;
    println!("查询结果: {:?}", count.unwrap());

    Ok(())
}
