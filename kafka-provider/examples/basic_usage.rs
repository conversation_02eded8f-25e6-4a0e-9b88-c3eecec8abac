use env_logger;
use futures::future::join_all;
use kafka_provider::{KafkaConfig, KafkaProvider, KafkaProviderError, KafkaProviderImpl};
use log::{error, info, LevelFilter};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::time::Duration;
use tokio::time::sleep;

// 定义消息结构体，用于序列化/反序列化
#[derive(Debug, Serialize, Deserialize, Clone)]
struct ProductEvent {
    id: i32,
    name: String,
    price: f64,
    event_type: String,
    timestamp: i64,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::Builder::from_default_env()
        .format_timestamp_millis()
        .format_module_path(false)
        .filter_level(LevelFilter::Info)
        .init();
    
    // 从环境变量获取Kafka配置
    let config = KafkaConfig {
        brokers: vec![env::var("KAFKA_BROKERS").unwrap_or_else(|_| "dev-gdp01.guwave.com:6667".to_string())],
        client_id: env::var("KAFKA_CLIENT_ID").unwrap_or_else(|_| "kafka-example-client".to_string()),
        group_id: Some(env::var("KAFKA_GROUP_ID").unwrap_or_else(|_| "OnedataDatawareGdpScheduler".to_string())),
        compression: Some("gzip".to_string()),
        connection_timeout_ms: 5000,
    };

    info!("尝试连接到Kafka集群: {:?}", config.brokers);

    match run_example(config).await {
        Ok(_) => info!("示例执行完成!"),
        Err(e) => error!("示例执行失败: {}", e),
    }

    Ok(())
}

async fn run_example(config: KafkaConfig) -> Result<(), KafkaProviderError> {
    // 创建Kafka提供者
    let provider = KafkaProviderImpl::new(config)?;
    info!("成功连接到Kafka!");

    // 定义主题名称
    let topic = "rust-kafka-product-events";
    
    // 创建示例消息
    let events = vec![
        ProductEvent {
            id: 1,
            name: "笔记本电脑".to_string(),
            price: 5999.99,
            event_type: "创建".to_string(),
            timestamp: chrono::Utc::now().timestamp(),
        },
        ProductEvent {
            id: 2,
            name: "智能手机".to_string(),
            price: 2999.99,
            event_type: "创建".to_string(),
            timestamp: chrono::Utc::now().timestamp(),
        },
        ProductEvent {
            id: 1,
            name: "高性能笔记本电脑".to_string(),
            price: 6499.99,
            event_type: "更新".to_string(),
            timestamp: chrono::Utc::now().timestamp(),
        },
    ];

    // 发送消息到Kafka
    info!("开始发送产品事件消息...");
    
    // 串行发送消息
    for (index, event) in events.iter().enumerate() {
        let key = format!("product-{}", event.id);
        let event_clone = event.clone();  // 克隆事件以获取所有权
        provider.send(topic, None, event_clone).await?;
        info!("已发送事件 {}/{}: {:?}", index + 1, events.len(), event);
    }

    // 短暂等待消息传递
    info!("等待2秒钟以确保消息已传递...");
    sleep(Duration::from_secs(2)).await;

    // 消费消息
    info!("开始消费产品事件消息...");
    let consumed_events: Vec<ProductEvent> = provider
        .consume(topic, Duration::from_secs(5))
        .await?;

    info!("已消费 {} 条产品事件:", consumed_events.len());
    for (index, event) in consumed_events.iter().enumerate() {
        info!(
            "事件 {}/{}: ID={}, 名称={}, 价格={}, 类型={}",
            index + 1,
            consumed_events.len(),
            event.id,
            event.name,
            event.price,
            event.event_type
        );
    }

    // 演示批量发送
    info!("演示批量发送消息...");
    
    let batch_events = vec![
        ProductEvent {
            id: 3,
            name: "无线耳机".to_string(),
            price: 999.99,
            event_type: "创建".to_string(),
            timestamp: chrono::Utc::now().timestamp(),
        },
        ProductEvent {
            id: 4,
            name: "智能手表".to_string(),
            price: 1599.99,
            event_type: "创建".to_string(),
            timestamp: chrono::Utc::now().timestamp(),
        },
    ];

    // 并行发送消息
    let mut send_futures = Vec::new();
    for mut event in batch_events {
        let key = format!("product-{}", event.id);
        let future = provider.send(topic, Some(key), event);
        send_futures.push(future);
    }
    
    // 等待所有消息发送完成
    join_all(send_futures).await;
    info!("批量消息发送完成");

    // 演示subscribe_and_process方法
    info!("演示长期订阅处理（将在后台运行3秒）...");
    
    // 创建一个任务来持续处理消息
    let topic_clone = topic.to_string();
    let provider_clone = provider.clone();
    let handle = tokio::spawn(async move {
        let _ = provider_clone.subscribe_and_process(&topic_clone, |event: ProductEvent| {
            info!("实时处理事件: ID={}, 名称={}", event.id, event.name);
            Ok(())
        }).await;
    });

    // 发送一条新消息用于演示
    sleep(Duration::from_secs(1)).await;
    let new_event = ProductEvent {
        id: 5,
        name: "智能音箱".to_string(),
        price: 399.99,
        event_type: "创建".to_string(),
        timestamp: chrono::Utc::now().timestamp(),
    };
    provider.send(topic, None, new_event).await?;
    
    // 让后台任务运行一段时间
    sleep(Duration::from_secs(3)).await;
    handle.abort();
    info!("长期订阅演示结束");
    
    Ok(())
} 