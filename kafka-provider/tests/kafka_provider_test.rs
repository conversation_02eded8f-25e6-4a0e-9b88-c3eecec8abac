use kafka_provider::{KafkaConfig, KafkaProvider, KafkaProviderImpl};
use std::collections::HashMap;
use std::time::Duration;
use serde::{Serialize, Deserialize};

// 示例消息结构体
#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
pub struct TestMessage {
    pub id: i32,
    pub content: String,
    pub created_at: u64,
}

// 辅助函数：从环境变量获取Kafka配置
fn get_test_config() -> KafkaConfig {
    // 获取测试用的Kafka broker地址，默认为dev-gdp01.guwave.com:6667
    let broker = std::env::var("TEST_KAFKA_BROKERS").unwrap_or_else(|_| "dev-gdp01.guwave.com:6667".to_string());
    
    KafkaConfig {
        brokers: vec![broker],
        client_id: "kafka-test-client".to_string(),
        group_id: Some("OnedataDatawareGdpScheduler-test".to_string()),
        compression: None,
        connection_timeout_ms: 10000,
    }
}

// 注意：要运行这些测试需要一个正在运行的Kafka服务器
// 这些测试通常会被标记为 #[ignore]，除非明确要求在CI环境中运行

// 测试连接到Kafka
#[tokio::test]
#[ignore] // 忽略此测试，除非明确运行 cargo test -- --ignored
async fn test_connection() {
    let _ = env_logger::try_init();
    let config = get_test_config();
    let provider = KafkaProviderImpl::new(config);
    assert!(provider.is_ok(), "无法连接到Kafka服务器");
}

// 测试发送和消费消息
#[tokio::test]
#[ignore]
async fn test_send_and_consume() {
    let _ = env_logger::try_init();
    let config = get_test_config();
    let provider = KafkaProviderImpl::new(config).unwrap();

    // 测试主题
    let topic = "test-rust-messages";
    
    // 创建测试消息
    let test_message = TestMessage {
        id: 1,
        content: "Hello, Kafka!".to_string(),
        created_at: chrono::Utc::now().timestamp() as u64,
    };

    // 发送消息
    provider.send(topic, Some("test-key".to_string()), test_message.clone())
        .await
        .expect("发送消息失败");

    // 等待消息传播
    tokio::time::sleep(Duration::from_secs(1)).await;

    // 消费消息
    let messages: Vec<TestMessage> = provider
        .consume(topic, Duration::from_secs(5))
        .await
        .expect("消费消息失败");

    // 验证收到的消息
    assert!(!messages.is_empty(), "未收到任何消息");
    
    // 查找匹配的消息
    let found = messages.iter().any(|msg| msg.id == test_message.id && msg.content == test_message.content);
    assert!(found, "未找到发送的测试消息");
}

// 测试消息处理器
#[tokio::test]
#[ignore]
async fn test_message_handler() {
    use std::sync::{Arc, Mutex};
    
    let _ = env_logger::try_init();
    let config = get_test_config();
    let provider = KafkaProviderImpl::new(config).unwrap();

    // 测试主题
    let topic = "test-rust-messages";
    
    // 发送多条测试消息
    for i in 1..=3 {
        let test_message = TestMessage {
            id: i,
            content: format!("Message {}", i),
            created_at: chrono::Utc::now().timestamp() as u64,
        };
        
        provider.send(topic, None, test_message)
            .await
            .expect("发送消息失败");
    }

    // 等待消息传播
    tokio::time::sleep(Duration::from_secs(1)).await;
    
    // 使用共享计数器跟踪处理的消息数
    let counter = Arc::new(Mutex::new(0));
    
    // 消费消息并手动处理
    let messages: Vec<TestMessage> = provider
        .consume(topic, Duration::from_secs(5))
        .await
        .expect("消费消息失败");
        
    // 处理每条消息
    for msg in messages {
        println!("处理消息: {:?}", msg);
        let mut count = counter.lock().unwrap();
        *count += 1;
    }
    
    // 验证处理的消息数
    let processed_count = *counter.lock().unwrap();
    assert!(processed_count > 0, "未处理任何消息");
    println!("共处理了 {} 条消息", processed_count);
} 