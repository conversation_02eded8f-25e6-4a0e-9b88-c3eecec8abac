use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use anyhow::Result;
use async_trait::async_trait;
use futures::future::join_all;
use kafka::client::{Compression, KafkaClient};
use kafka::consumer::{Consumer, FetchOffset};
use kafka::error::Error as KafkaError;
use kafka::producer::{Producer, Record};
use serde::{de::DeserializeOwned, Serialize};
use thiserror::Error;
use tokio::task;

// 定义Kafka连接错误
#[derive(Debug, Error)]
pub enum KafkaProviderError {
    #[error("Kafka连接错误: {0}")]
    ConnectionError(String),
    #[error("消息发送错误: {0}")]
    ProducerError(String),
    #[error("消息接收错误: {0}")]
    ConsumerError(String),
    #[error("序列化错误: {0}")]
    SerializationError(String),
    #[error("反序列化错误: {0}")]
    DeserializationError(String),
}

// 定义Kafka配置
#[derive(Clone, Debug)]
pub struct KafkaConfig {
    pub brokers: Vec<String>,
    pub client_id: String,
    pub group_id: Option<String>,
    pub compression: Option<String>,
    pub connection_timeout_ms: u64,
}

impl Default for KafkaConfig {
    fn default() -> Self {
        Self {
            brokers: vec!["dev-gdp01.guwave.com:6667".to_string()],
            client_id: "kafka-client".to_string(),
            group_id: Some("OnedataDatawareGdpScheduler".to_string()),
            compression: None,
            connection_timeout_ms: 5000,
        }
    }
}

// 定义Kafka提供者接口
#[async_trait]
pub trait KafkaProvider {
    // 发送消息到指定主题
    async fn send<T>(&self, topic: &str, key: Option<String>, message: T) -> Result<(), KafkaProviderError>
    where
        T: Serialize + Send + Sync + 'static;

    // 发送原始字符串消息到指定主题
    async fn send_raw(&self, topic: &str, key: Option<String>, message: String) -> Result<(), KafkaProviderError>;

    // 从指定主题消费消息并反序列化为类型T
    async fn consume<T>(&self, topic: &str, timeout: Duration) -> Result<Vec<T>, KafkaProviderError>
    where
        T: DeserializeOwned + Send + 'static;

    // 订阅主题并处理消息
    async fn subscribe_and_process<T, F>(&self, topic: &str, handler: F) -> Result<(), KafkaProviderError>
    where
        T: DeserializeOwned + Send + 'static,
        F: FnMut(T) -> Result<(), KafkaProviderError> + Send + 'static;
}

// Kafka提供者实现
#[derive(Clone)]
pub struct KafkaProviderImpl {
    config: Arc<KafkaConfig>,
}

impl KafkaProviderImpl {
    // 创建Kafka提供者实例
    pub fn new(config: KafkaConfig) -> Result<Self, KafkaProviderError> {
        Ok(Self { config: Arc::new(config) })
    }
}

#[async_trait]
impl KafkaProvider for KafkaProviderImpl {
    // 发送序列化后的消息
    async fn send<T>(&self, topic: &str, key: Option<String>, message: T) -> Result<(), KafkaProviderError>
    where
        T: Serialize + Send + Sync + 'static,
    {
        let payload =
            serde_json::to_string(&message).map_err(|e| KafkaProviderError::SerializationError(e.to_string()))?;

        self.send_raw(topic, key, payload).await
    }

    // 发送原始消息
    async fn send_raw(&self, topic: &str, key: Option<String>, message: String) -> Result<(), KafkaProviderError> {
        let start = std::time::Instant::now();
        log::info!("开始发送消息到主题 {}: {}", topic, message);

        // kafka-rust不是异步库，需要在tokio任务中运行以避免阻塞
        let config = self.config.clone();
        let topic = topic.to_owned();
        let key = key; // 不需要map转换了
                       // let message = message.to_owned(); // 不再需要，因为已经拥有所有权

        // 在单独的线程中执行同步操作
        task::spawn_blocking(move || {
            let result = || -> Result<(), KafkaError> {
                let mut producer = Producer::from_hosts(config.brokers.clone())
                    .with_ack_timeout(Duration::from_millis(config.connection_timeout_ms))
                    .with_client_id(config.client_id.clone())
                    .create()?;

                // 根据是否有key创建不同的Record
                if let Some(k) = key {
                    producer.send(&Record::from_key_value(&topic, k, message))?;
                } else {
                    producer.send(&Record::from_value(&topic, message))?;
                }

                Ok(())
            }();

            match result {
                Ok(_) => Ok(()),
                Err(e) => Err(KafkaProviderError::ProducerError(e.to_string())),
            }
        })
        .await
        .map_err(|e| KafkaProviderError::ProducerError(e.to_string()))??;

        log::info!("消息发送成功，耗时: {:?}", start.elapsed());
        Ok(())
    }

    // 消费消息并反序列化
    async fn consume<T>(&self, topic: &str, timeout: Duration) -> Result<Vec<T>, KafkaProviderError>
    where
        T: DeserializeOwned + Send + 'static,
    {
        let start = std::time::Instant::now();
        log::info!("开始从主题 {} 消费消息，超时时间: {:?}", topic, timeout);

        // 拷贝数据以便传递到新任务
        let config = self.config.clone();
        let topic = topic.to_owned();

        // 在tokio线程池中执行同步操作
        let messages = task::spawn_blocking(move || -> Result<Vec<T>, KafkaProviderError> {
            let mut messages = Vec::new();

            // 创建消费者
            let mut consumer = match Consumer::from_hosts(config.brokers.clone())
                .with_client_id(config.client_id.clone())
                .with_group(
                    config
                        .group_id
                        .clone()
                        .unwrap_or_else(|| "OnedataDatawareGdpScheduler".to_string()),
                )
                .with_topic(topic.clone()) // 订阅主题的所有分区
                .with_fallback_offset(FetchOffset::Earliest)
                .with_fetch_max_wait_time(Duration::from_secs(1))
                .with_fetch_min_bytes(1000)
                .with_fetch_max_bytes_per_partition(100_000)
                .create()
            {
                Ok(consumer) => consumer,
                Err(e) => return Err(KafkaProviderError::ConsumerError(e.to_string())),
            };

            let deadline = start + timeout;
            while std::time::Instant::now() < deadline {
                // 尝试消费消息
                match consumer.poll() {
                    Ok(message_sets) => {
                        // 使用.iter()方法迭代MessageSets
                        for ms in message_sets.iter() {
                            for message in ms.messages() {
                                let payload = match std::str::from_utf8(message.value) {
                                    Ok(s) => s,
                                    Err(e) => {
                                        log::error!("无法解析消息为UTF-8字符串: {}", e);
                                        continue;
                                    }
                                };

                                log::info!("接收到消息: {}", payload);

                                match serde_json::from_str::<T>(payload) {
                                    Ok(deserialized) => messages.push(deserialized),
                                    Err(e) => log::error!("反序列化消息失败: {}", e),
                                }
                            }

                            // 提交消息偏移量
                            if let Err(e) = consumer.consume_messageset(ms) {
                                log::error!("提交偏移量失败: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        log::error!("轮询消息失败: {}", e);
                        std::thread::sleep(Duration::from_millis(100));
                    }
                }

                // 短暂休眠，避免CPU占用过高
                std::thread::sleep(Duration::from_millis(100));
            }

            Ok(messages)
        })
        .await
        .map_err(|e| KafkaProviderError::ConsumerError(e.to_string()))??;

        log::info!("消息消费完成，接收到 {} 条消息，耗时: {:?}", messages.len(), start.elapsed());
        Ok(messages)
    }

    // 订阅主题并处理消息
    async fn subscribe_and_process<T, F>(&self, topic: &str, mut handler: F) -> Result<(), KafkaProviderError>
    where
        T: DeserializeOwned + Send + 'static,
        F: FnMut(T) -> Result<(), KafkaProviderError> + Send + 'static,
    {
        // 拷贝数据以便传递到新任务
        let config = self.config.clone();
        let topic = topic.to_owned();

        // 在tokio线程池中执行同步操作
        task::spawn_blocking(move || -> Result<(), KafkaProviderError> {
            // 创建消费者
            let mut consumer = match Consumer::from_hosts(config.brokers.clone())
                .with_client_id(config.client_id.clone())
                .with_group(
                    config
                        .group_id
                        .clone()
                        .unwrap_or_else(|| "OnedataDatawareGdpScheduler".to_string()),
                )
                .with_topic(topic.clone()) // 订阅主题的所有分区
                .with_fallback_offset(FetchOffset::Earliest)
                .with_fetch_max_wait_time(Duration::from_secs(1))
                .with_fetch_min_bytes(1000)
                .with_fetch_max_bytes_per_partition(100_000)
                .create()
            {
                Ok(consumer) => consumer,
                Err(e) => return Err(KafkaProviderError::ConsumerError(e.to_string())),
            };

            log::info!("开始消费消息 {}", topic);

            // 无限循环，持续消费消息
            loop {
                // 尝试消费消息
                match consumer.poll() {
                    Ok(message_sets) => {
                        // 使用.iter()方法迭代MessageSets
                        for ms in message_sets.iter() {
                            for message in ms.messages() {
                                let payload = match std::str::from_utf8(message.value) {
                                    Ok(s) => s,
                                    Err(e) => {
                                        log::error!("无法解析消息为UTF-8字符串: {}", e);
                                        continue;
                                    }
                                };

                                match serde_json::from_str::<T>(payload) {
                                    Ok(deserialized) => {
                                        if let Err(e) = handler(deserialized) {
                                            log::error!("处理消息失败: {}", e);
                                        }
                                    }
                                    Err(e) => log::error!("反序列化消息失败: {}", e),
                                }
                            }

                            // 提交消息偏移量
                            if let Err(e) = consumer.consume_messageset(ms) {
                                log::error!("提交偏移量失败: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        log::error!("消费消息失败: {}", e);
                        std::thread::sleep(Duration::from_millis(100));
                    }
                }
                // 短暂休眠，避免CPU占用过高
                std::thread::sleep(Duration::from_millis(100));
            }
        });
        Ok(())
    }
}
