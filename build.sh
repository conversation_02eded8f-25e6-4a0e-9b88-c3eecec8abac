#!/bin/bash
DATAWARE_VERSION="3.4.5"
CRATE_NAME="native"
echo "Crate Name: $CRATE_NAME"
echo "Target Version: $DATAWARE_VERSION"

echo "Building the native library with Cargo..."
cargo build --release

SOURCE_FILE="target/release/lib${CRATE_NAME}.so"
DEST_FILE="target/release/lib${CRATE_NAME}-${DATAWARE_VERSION}.so"

echo "Renaming ${SOURCE_FILE} to ${DEST_FILE}"
mv "${SOURCE_FILE}" "${DEST_FILE}"

echo "Build complete. Final artifact: ${DEST_FILE}"