use crate::constants::RedisKeyConstants;
use common::model::constant::MIDDLE_LINE;
use common::model::key::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};

/// Redis key generation utilities
/// Corresponds to: RedisKeyGenerateUtil in Java implementation
pub struct RedisKeyGenerator;

impl RedisKeyGenerator {
    /// Generate Redis key for test program test order
    /// Corresponds to: generateRedisKeyTestProgramTestOrder method
    pub fn generate_test_program_test_order_key(
        customer: &str,
        sub_customer: &str,
        upload_type: &str,
        test_area: &str,
        factory: &str,
        factory_site: &str,
        device_id: &str,
        test_stage: &str,
        lot_type: &str,
        test_program: &str,
    ) -> String {
        format!(
            "{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}",
            RedisKeyConstants::TEST_PROGRAM_TEST_ORDER_PREFIX,
            customer,
            MIDDLE_LINE,
            sub_customer,
            MIDDLE_LINE,
            upload_type,
            MIDDLE_LINE,
            test_area,
            MIDDLE_LINE,
            factory,
            MIDDLE_LINE,
            factory_site,
            MIDDLE_LINE,
            device_id,
            MIDDLE_LINE,
            test_stage,
            MIDDLE_LINE,
            lot_type,
            MIDDLE_LINE,
            test_program
        )
    }

    /// Generate Redis key for test program test plan
    /// Corresponds to: generateRedisKeyTestProgramTestPlan method
    pub fn generate_test_program_test_plan_key(
        customer: &str,
        sub_customer: &str,
        upload_type: &str,
        test_area: &str,
        factory: &str,
        factory_site: &str,
        device_id: &str,
        test_stage: &str,
        lot_type: &str,
        test_program: &str,
    ) -> String {
        format!(
            "{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}",
            RedisKeyConstants::TEST_PROGRAM_TEST_PLAN_PREFIX,
            customer,
            MIDDLE_LINE,
            sub_customer,
            MIDDLE_LINE,
            upload_type,
            MIDDLE_LINE,
            test_area,
            MIDDLE_LINE,
            factory,
            MIDDLE_LINE,
            factory_site,
            MIDDLE_LINE,
            device_id,
            MIDDLE_LINE,
            test_stage,
            MIDDLE_LINE,
            lot_type,
            MIDDLE_LINE,
            test_program
        )
    }

    /// Generate Redis key for YMS die
    /// Corresponds to: generateRedisKeyYmsDie method
    pub fn generate_yms_die_key(
        customer: &str,
        sub_customer: &str,
        factory: &str,
        factory_site: &str,
        test_area: &str,
        test_stage: &str,
        device_id: &str,
        lot_type: &str,
        lot_id: &str,
        wafer_no: &str,
    ) -> String {
        format!(
            "{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}",
            RedisKeyConstants::YMS_DIE_PREFIX,
            customer,
            MIDDLE_LINE,
            sub_customer,
            MIDDLE_LINE,
            factory,
            MIDDLE_LINE,
            factory_site,
            MIDDLE_LINE,
            test_area,
            MIDDLE_LINE,
            test_stage,
            MIDDLE_LINE,
            device_id,
            MIDDLE_LINE,
            lot_type,
            MIDDLE_LINE,
            lot_id,
            MIDDLE_LINE,
            wafer_no
        )
    }

    /// Generate Redis key for test program test order using LotKey
    pub fn generate_test_program_test_order_key_from_lot(
        lot_key: &LotKey,
        upload_type: &str,
        test_program: &str,
    ) -> String {
        Self::generate_test_program_test_order_key(
            &lot_key.customer,
            &lot_key.sub_customer,
            upload_type,
            &lot_key.test_area,
            &lot_key.factory,
            &lot_key.factory_site,
            &lot_key.device_id,
            &lot_key.test_stage,
            &lot_key.lot_type,
            test_program,
        )
    }

    /// Generate Redis key for test program test plan using LotKey
    pub fn generate_test_program_test_plan_key_from_lot(
        lot_key: &LotKey,
        upload_type: &str,
        test_program: &str,
    ) -> String {
        Self::generate_test_program_test_plan_key(
            &lot_key.customer,
            &lot_key.sub_customer,
            upload_type,
            &lot_key.test_area,
            &lot_key.factory,
            &lot_key.factory_site,
            &lot_key.device_id,
            &lot_key.test_stage,
            &lot_key.lot_type,
            test_program,
        )
    }

    /// Generate Redis key for YMS die using WaferKey
    pub fn generate_yms_die_key_from_wafer(wafer_key: &WaferKey) -> String {
        Self::generate_yms_die_key(
            &wafer_key.customer,
            &wafer_key.sub_customer,
            &wafer_key.factory,
            &wafer_key.factory_site,
            &wafer_key.test_area,
            &wafer_key.test_stage,
            &wafer_key.device_id,
            &wafer_key.lot_type,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_test_program_test_order_key() {
        let key = RedisKeyGenerator::generate_test_program_test_order_key(
            "customer1",
            "sub_customer1",
            "upload_type1",
            "test_area1",
            "factory1",
            "factory_site1",
            "device1",
            "test_stage1",
            "lot_type1",
            "test_program1",
        );
        
        assert_eq!(
            key,
            "test_program_test_order:customer1-sub_customer1-upload_type1-test_area1-factory1-factory_site1-device1-test_stage1-lot_type1-test_program1"
        );
    }

    #[test]
    fn test_generate_yms_die_key() {
        let key = RedisKeyGenerator::generate_yms_die_key(
            "customer1",
            "sub_customer1",
            "factory1",
            "factory_site1",
            "test_area1",
            "test_stage1",
            "device1",
            "lot_type1",
            "lot_id1",
            "wafer_no1",
        );
        
        assert_eq!(
            key,
            "yms_die:customer1-sub_customer1-factory1-factory_site1-test_area1-test_stage1-device1-lot_type1-lot_id1-wafer_no1"
        );
    }
}