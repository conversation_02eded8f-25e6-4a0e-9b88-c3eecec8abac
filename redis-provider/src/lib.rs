//! Redis Provider - Redis读写公共组件
//! 
//! 提供Redis分布式锁和数据操作功能，对应Java/Scala实现的RedisProvider类
//! 
//! # 主要功能
//! - 分布式锁管理
//! - Redis key生成工具
//! - 测试程序锁管理
//! - YMS die锁管理
//! 
//! # 使用示例
//! 
//! ```rust,no_run
//! use common::model::key::LotKey;
//! use redis_provider::{RedisProvider};

//! // 创建Redis提供者
//! let provider = RedisProvider::new("redis://localhost:6379", Some("password")).unwrap();

//! // 创建LotKey
//! let lot_key = LotKey::new(
//!     "customer".to_string(),
//!     "sub_customer".to_string(),
//!     "test_area".to_string(),
//!     "factory".to_string(),
//!     "factory_site".to_string(),
//!     "device_id".to_string(),
//!     "lot_id".to_string(),
//!     "test_stage".to_string(),
//!     "lot_type".to_string(),
//! );
//!
//! // 获取测试程序锁
//! let mut lock = provider.get_test_program_test_order_lock(&lot_key, "upload_type", "test_program");
//! lock.lock().unwrap();
//! // ... 执行需要锁保护的操作
//! lock.unlock().unwrap();
//! ```

pub mod constants;
pub mod key_generator;
pub mod lock;
pub mod models;
pub mod provider;

// Re-export commonly used types
pub use constants::RedisKeyConstants;
pub use key_generator::RedisKeyGenerator;
pub use lock::{RedisLock, LockError};
pub use provider::{RedisProvider, RedisProviderError};
