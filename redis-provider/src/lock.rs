use redis::{Client, RedisResult};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum LockError {
    #[error("Redis error: {0}")]
    Redis(#[from] redis::RedisError),
    #[error("Lock acquisition failed")]
    AcquisitionFailed,
    #[error("Lock not held")]
    NotHeld,
    #[error("Lock expired")]
    Expired,
}

/// Redis distributed lock implementation
/// Corresponds to: RLock interface in Redisson
pub struct RedisLock {
    client: Client,
    key: String,
    value: String,
    ttl: Duration,
    acquired: bool,
}

impl RedisLock {
    pub fn new(client: Client, key: String, ttl: Duration) -> Self {
        let value = format!("{}:{}", 
            SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis(),
            std::process::id()
        );
        
        Self {
            client,
            key,
            value,
            ttl,
            acquired: false,
        }
    }

    /// Acquire the lock
    /// Corresponds to: RLock.lock() method
    pub fn lock(&mut self) -> Result<(), LockError> {
        let mut conn = self.client.get_connection()?;
        
        // Use SET with NX (only if not exists) and EX (expiration)
        let result: RedisResult<String> = redis::cmd("SET")
            .arg(&self.key)
            .arg(&self.value)
            .arg("NX")
            .arg("EX")
            .arg(self.ttl.as_secs())
            .query(&mut conn);

        match result {
            Ok(_) => {
                self.acquired = true;
                Ok(())
            }
            Err(_) => Err(LockError::AcquisitionFailed),
        }
    }

    /// Try to acquire the lock without blocking
    /// Corresponds to: RLock.tryLock() method
    pub fn try_lock(&mut self) -> Result<bool, LockError> {
        match self.lock() {
            Ok(()) => Ok(true),
            Err(LockError::AcquisitionFailed) => Ok(false),
            Err(e) => Err(e),
        }
    }

    /// Try to acquire the lock with timeout
    /// Corresponds to: RLock.tryLock(time, unit) method
    pub fn try_lock_with_timeout(&mut self, timeout: Duration) -> Result<bool, LockError> {
        let start = SystemTime::now();
        
        loop {
            if self.try_lock()? {
                return Ok(true);
            }
            
            if start.elapsed().unwrap() >= timeout {
                return Ok(false);
            }
            
            // Small delay before retry
            std::thread::sleep(Duration::from_millis(10));
        }
    }

    /// Release the lock
    /// Corresponds to: RLock.unlock() method
    pub fn unlock(&mut self) -> Result<(), LockError> {
        if !self.acquired {
            return Err(LockError::NotHeld);
        }

        let mut conn = self.client.get_connection()?;
        
        // Lua script to ensure we only delete our own lock
        let script = r#"
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
        "#;
        
        let result: i32 = redis::Script::new(script)
            .key(&self.key)
            .arg(&self.value)
            .invoke(&mut conn)?;
            
        if result == 1 {
            self.acquired = false;
            Ok(())
        } else {
            Err(LockError::NotHeld)
        }
    }

    /// Check if the lock is held by this instance
    pub fn is_held(&self) -> bool {
        self.acquired
    }

    /// Get the lock key
    pub fn key(&self) -> &str {
        &self.key
    }
}

impl Drop for RedisLock {
    fn drop(&mut self) {
        if self.acquired {
            let _ = self.unlock();
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_lock_creation() {
        let client = Client::open("redis://127.0.0.1/").unwrap();
        let lock = RedisLock::new(client, "test_key".to_string(), Duration::from_secs(30));
        
        assert_eq!(lock.key(), "test_key");
        assert!(!lock.is_held());
    }
}