use crate::key_generator::Red<PERSON><PERSON>eyGenerator;
use crate::lock::{RedisLock, LockError};
use redis::Client;
use std::time::Duration;
use thiserror::Error;
use common::model::key::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};

#[derive(Error, Debug)]
pub enum RedisProviderError {
    #[error("Redis connection error: {0}")]
    Connection(#[from] redis::RedisError),
    #[error("Lock error: {0}")]
    Lock(#[from] LockError),
    #[error("Invalid configuration: {0}")]
    Config(String),
}

/// Redis provider for distributed locking and data operations
/// Corresponds to: RedisProvider class in Scala implementation
pub struct RedisProvider {
    client: Client,
    default_lock_ttl: Duration,
}

impl RedisProvider {
    /// Create a new RedisProvider instance
    /// Corresponds to: RedisProvider constructor in Scala
    pub fn new(redis_address: &str, redis_password: Option<&str>) -> Result<Self, RedisProviderError> {
        let connection_string = if let Some(password) = redis_password {
            format!("redis://:{}@{}", password, redis_address.trim_start_matches("redis://"))
        } else {
            redis_address.to_string()
        };

        let client = Client::open(connection_string)?;
        
        // Test connection
        let mut conn = client.get_connection()?;
        let _: String = redis::cmd("PING").query(&mut conn)?;

        Ok(Self {
            client,
            default_lock_ttl: Duration::from_secs(300), // 5 minutes default
        })
    }

    /// Create a new RedisProvider with custom lock TTL
    pub fn with_lock_ttl(redis_address: &str, redis_password: Option<&str>, lock_ttl: Duration) -> Result<Self, RedisProviderError> {
        let mut provider = Self::new(redis_address, redis_password)?;
        provider.default_lock_ttl = lock_ttl;
        Ok(provider)
    }

    /// Get a distributed lock for the given key
    /// Corresponds to: getLock method in Scala
    pub fn get_lock(&self, key: &str) -> RedisLock {
        RedisLock::new(self.client.clone(), key.to_string(), self.default_lock_ttl)
    }

    /// Get a distributed lock with custom TTL
    pub fn get_lock_with_ttl(&self, key: &str, ttl: Duration) -> RedisLock {
        RedisLock::new(self.client.clone(), key.to_string(), ttl)
    }

    /// Get test program test order lock
    /// Corresponds to: getTestProgramTestOrderLock method in Scala
    pub fn get_test_program_test_order_lock(
        &self,
        lot_key: &LotKey,
        upload_type: &str,
        test_program: &str,
    ) -> RedisLock {
        let key = RedisKeyGenerator::generate_test_program_test_order_key_from_lot(
            lot_key, upload_type, test_program
        );
        self.get_lock(&key)
    }

    /// Get test program test plan lock
    /// Corresponds to: getTestProgramTestPlanLock method in Scala
    pub fn get_test_program_test_plan_lock(
        &self,
        lot_key: &LotKey,
        upload_type: &str,
        test_program: &str,
    ) -> RedisLock {
        let key = RedisKeyGenerator::generate_test_program_test_plan_key_from_lot(
            lot_key, upload_type, test_program
        );
        self.get_lock(&key)
    }

    /// Get YMS die lock
    /// Corresponds to: getYmsDieLock method in Scala
    pub fn get_yms_die_lock(&self, wafer_key: &WaferKey) -> RedisLock {
        let key = RedisKeyGenerator::generate_yms_die_key_from_wafer(wafer_key);
        self.get_lock(&key)
    }

    /// Test Redis connection
    pub fn ping(&self) -> Result<String, RedisProviderError> {
        let mut conn = self.client.get_connection()?;
        let result: String = redis::cmd("PING").query(&mut conn)?;
        Ok(result)
    }

    /// Get Redis client for advanced operations
    pub fn client(&self) -> &Client {
        &self.client
    }
}

// Implement Drop to ensure proper cleanup
// Corresponds to: close() method in Scala
impl Drop for RedisProvider {
    fn drop(&mut self) {
        // Redis client automatically handles connection cleanup
        // No explicit shutdown needed like in Redisson
    }
}

#[cfg(test)]
mod tests {
    use common::model::key::LotKey;
    use super::*;

    #[test]
    fn test_redis_provider_creation() {
        // This test requires a running Redis instance
        // Skip if Redis is not available
        if let Ok(provider) = RedisProvider::new("redis://127.0.0.1:6379", None) {
            assert!(provider.ping().is_ok());
        }
    }

    #[test]
    fn test_lock_key_generation() {
        let provider = RedisProvider::new("redis://127.0.0.1:6379", None);
        if provider.is_err() {
            return; // Skip test if Redis not available
        }
        let provider = provider.unwrap();

        let lot_key = LotKey::new(
            "customer1".to_string(),
            "sub_customer1".to_string(),
            "test_area1".to_string(),
            "factory1".to_string(),
            "factory_site1".to_string(),
            "device1".to_string(),
            "test_lot1".to_string(),
            "test_stage1".to_string(),
            "lot_type1".to_string(),
        );

        let lock = provider.get_test_program_test_order_lock(&lot_key, "upload_type1", "test_program1");
        assert!(lock.key().contains("test_program_test_order"));
        assert!(lock.key().contains("customer1"));
    }

    #[test]
    fn test_wafer_key_lock() {
        let provider = RedisProvider::new("redis://127.0.0.1:6379", None);
        if provider.is_err() {
            return; // Skip test if Redis not available
        }
        let provider = provider.unwrap();

        let wafer_key = WaferKey::new(
            "customer1".to_string(),
            "sub_customer1".to_string(),
            "factory1".to_string(),
            "factory_site1".to_string(),
            "test_area1".to_string(),
            "test_stage1".to_string(),
            "device1".to_string(),
            "lot_type1".to_string(),
            "lot_id1".to_string(),
            "wafer_no1".to_string(),
        );

        let lock = provider.get_yms_die_lock(&wafer_key);
        assert!(lock.key().contains("yms_die"));
        assert!(lock.key().contains("customer1"));
    }
}