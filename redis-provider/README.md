# Redis Provider

Redis读写公共组件，提供分布式锁和Redis操作功能。这是对原Java/Scala RedisProvider类的Rust实现。

## 功能特性

- **分布式锁管理**: 基于Redis的分布式锁实现，支持锁获取、释放和超时控制
- **Redis Key生成**: 统一的Redis key生成工具，确保key格式的一致性
- **测试程序锁**: 专门针对测试程序测试顺序和测试计划的锁管理
- **YMS Die锁**: 针对YMS die操作的分布式锁
- **连接管理**: 自动的Redis连接管理和清理

## 依赖要求

- Redis服务器 (版本 >= 5.0)
- Rust (版本 >= 1.70)

## 安装

在你的 `Cargo.toml` 中添加：

```toml
[dependencies]
redis-provider = { path = "../redis-provider" }
```

## 基本使用

### 创建Redis提供者

```rust
use redis_provider::RedisProvider;

// 无密码连接
let provider = RedisProvider::new("redis://127.0.0.1:6379", None)?;

// 有密码连接
let provider = RedisProvider::new("redis://127.0.0.1:6379", Some("your_password"))?;

// 自定义锁TTL
let provider = RedisProvider::with_lock_ttl(
    "redis://127.0.0.1:6379", 
    None, 
    Duration::from_secs(600) // 10分钟TTL
)?;
```

### 使用LotKey锁

```rust
use redis_provider::{RedisProvider, LotKey};

let provider = RedisProvider::new("redis://127.0.0.1:6379", None)?;

let lot_key = LotKey::new(
    "YEESTOR".to_string(),
    "YEESTOR_SUB".to_string(),
    "CP".to_string(),
    "LEADYO".to_string(),
    "LEADYO_SITE".to_string(),
    "YS8293ENAB".to_string(),
    "CP1".to_string(),
    "PRODUCTION".to_string(),
);

// 获取测试程序测试顺序锁
let mut lock = provider.get_test_program_test_order_lock(
    &lot_key,
    "UPLOAD_TYPE",
    "TEST_PROGRAM"
);

// 尝试获取锁
if lock.try_lock()? {
    println!("成功获取锁");
    
    // 执行需要锁保护的操作
    // ...
    
    // 释放锁
    lock.unlock()?;
} else {
    println!("锁已被占用");
}
```

### 使用WaferKey锁

```rust
use redis_provider::{RedisProvider, WaferKey};

let wafer_key = WaferKey::new(
    "YEESTOR".to_string(),
    "YEESTOR_SUB".to_string(),
    "LEADYO".to_string(),
    "LEADYO_SITE".to_string(),
    "CP".to_string(),
    "CP1".to_string(),
    "YS8293ENAB".to_string(),
    "PRODUCTION".to_string(),
    "ENF083".to_string(),
    "25".to_string(),
);

// 获取YMS die锁
let mut yms_lock = provider.get_yms_die_lock(&wafer_key);

// 阻塞获取锁
yms_lock.lock()?;
println!("获取到YMS die锁");

// 自动释放锁（通过Drop trait）
```

### 锁操作方法

```rust
let mut lock = provider.get_lock("custom_key");

// 非阻塞尝试获取锁
let acquired = lock.try_lock()?;

// 带超时的锁获取
let acquired = lock.try_lock_with_timeout(Duration::from_secs(5))?;

// 阻塞获取锁
lock.lock()?;

// 检查锁状态
if lock.is_held() {
    println!("锁已被持有");
}

// 手动释放锁
lock.unlock()?;

// 获取锁的key
println!("锁键名: {}", lock.key());
```

## 错误处理

```rust
use redis_provider::{RedisProviderError, LockError};

match provider.get_lock("test").try_lock() {
    Ok(true) => println!("获取锁成功"),
    Ok(false) => println!("锁已被占用"),
    Err(LockError::Redis(e)) => println!("Redis错误: {}", e),
    Err(LockError::AcquisitionFailed) => println!("锁获取失败"),
    Err(e) => println!("其他错误: {}", e),
}
```

## 运行示例

```bash
# 启动Redis服务器
docker run -d -p 6379:6379 redis:latest

# 运行基本使用示例
cargo run --example basic_usage

# 运行测试
cargo test
```

## 架构对应关系

| Rust组件 | Java/Scala对应组件 | 说明 |
|---------|------------------|------|
| `RedisProvider` | `RedisProvider` | 主要的Redis提供者类 |
| `RedisLock` | `RLock` | 分布式锁实现 |
| `RedisKeyGenerator` | `RedisKeyGenerateUtil` | Redis key生成工具 |
| `LotKey` | `LotKey` | Lot标识符 |
| `WaferKey` | `WaferKey` | Wafer标识符 |

## 性能特性

- **连接复用**: 使用Redis连接池避免频繁连接创建
- **原子操作**: 使用Lua脚本确保锁操作的原子性
- **自动清理**: 通过Drop trait自动释放资源
- **超时控制**: 支持锁获取超时，避免死锁

## 注意事项

1. **锁TTL**: 默认锁TTL为5分钟，可根据业务需求调整
2. **网络异常**: 网络异常可能导致锁无法正常释放，依赖TTL自动过期
3. **Redis版本**: 建议使用Redis 5.0+以获得最佳性能
4. **并发安全**: 所有锁操作都是线程安全的

## 故障排除

### 连接问题
```rust
// 测试Redis连接
match provider.ping() {
    Ok(response) => println!("Redis连接正常: {}", response),
    Err(e) => println!("Redis连接失败: {}", e),
}
```

### 锁获取失败
- 检查Redis服务器状态
- 确认网络连接
- 检查锁key是否被其他进程占用
- 考虑增加锁获取超时时间

### 性能优化
- 使用连接池减少连接开销
- 合理设置锁TTL避免长时间占用
- 监控Redis内存使用情况