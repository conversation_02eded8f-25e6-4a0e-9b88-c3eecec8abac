use redis_provider::{<PERSON><PERSON><PERSON><PERSON><PERSON>, RedisKeyGenerator};
use std::time::Duration;
use std::thread;
use common::model::key::{<PERSON><PERSON><PERSON>, Wafer<PERSON>ey};
// 这些测试需要运行Redis服务器
// 可以使用 docker run -d -p 6379:6379 redis:latest 启动Redis

#[test]
fn test_redis_provider_basic_operations() {
    let provider = match RedisProvider::new("redis://127.0.0.1:6379", None) {
        Ok(p) => p,
        Err(_) => {
            println!("跳过测试 - Redis服务器不可用");
            return;
        }
    };

    // 测试连接
    assert!(provider.ping().is_ok());
}

#[test]
fn test_lot_key_locks() {
    let provider = match RedisProvider::new("redis://127.0.0.1:6379", None) {
        Ok(p) => p,
        Err(_) => {
            println!("跳过测试 - Redis服务器不可用");
            return;
        }
    };

    let lot_key = LotKey::new(
        "TEST_CUSTOMER".to_string(),
        "TEST_SUB_CUSTOMER".to_string(),
        "CP".to_string(),
        "TEST_FACTORY".to_string(),
        "TEST_SITE".to_string(),
        "TEST_DEVICE".to_string(),
        "TEST_LOT".to_string(),
        "CP1".to_string(),
        "PRODUCTION".to_string(),
    );

    // 测试测试程序测试顺序锁
    let mut lock1 = provider.get_test_program_test_order_lock(
        &lot_key,
        "UPLOAD_TYPE",
        "TEST_PROGRAM"
    );

    assert!(lock1.try_lock().unwrap());
    assert!(lock1.is_held());
    assert!(lock1.unlock().is_ok());
    assert!(!lock1.is_held());

    // 测试测试程序测试计划锁
    let mut lock2 = provider.get_test_program_test_plan_lock(
        &lot_key,
        "UPLOAD_TYPE",
        "TEST_PROGRAM"
    );

    assert!(lock2.try_lock().unwrap());
    assert!(lock2.unlock().is_ok());
}

#[test]
fn test_wafer_key_locks() {
    let provider = match RedisProvider::new("redis://127.0.0.1:6379", None) {
        Ok(p) => p,
        Err(_) => {
            println!("跳过测试 - Redis服务器不可用");
            return;
        }
    };

    let wafer_key = WaferKey::new(
        "TEST_CUSTOMER".to_string(),
        "TEST_SUB_CUSTOMER".to_string(),
        "TEST_FACTORY".to_string(),
        "TEST_SITE".to_string(),
        "CP".to_string(),
        "CP1".to_string(),
        "TEST_DEVICE".to_string(),
        "PRODUCTION".to_string(),
        "TEST_LOT".to_string(),
        "1".to_string(),
    );

    let mut yms_lock = provider.get_yms_die_lock(&wafer_key);
    
    assert!(yms_lock.try_lock().unwrap());
    assert!(yms_lock.is_held());
    assert!(yms_lock.unlock().is_ok());
}

#[test]
fn test_concurrent_locks() {
    let provider = match RedisProvider::new("redis://127.0.0.1:6379", None) {
        Ok(p) => p,
        Err(_) => {
            println!("跳过测试 - Redis服务器不可用");
            return;
        }
    };

    let provider = std::sync::Arc::new(provider);
    let mut handles = vec![];

    // 启动多个线程尝试获取同一个锁
    for i in 0..5 {
        let provider_clone = provider.clone();
        let handle = thread::spawn(move || {
            let mut lock = provider_clone.get_lock(&format!("test_concurrent_lock"));
            
            if lock.try_lock().unwrap_or(false) {
                println!("线程 {} 获取到锁", i);
                thread::sleep(Duration::from_millis(100));
                lock.unlock().unwrap();
                println!("线程 {} 释放锁", i);
                true
            } else {
                println!("线程 {} 未能获取锁", i);
                false
            }
        });
        handles.push(handle);
    }

    let mut success_count = 0;
    for handle in handles {
        if handle.join().unwrap() {
            success_count += 1;
        }
    }

    // 在并发情况下，只有一个线程应该能获取到锁
    assert_eq!(success_count, 1);
}

#[test]
fn test_lock_timeout() {
    let provider = match RedisProvider::new("redis://127.0.0.1:6379", None) {
        Ok(p) => p,
        Err(_) => {
            println!("跳过测试 - Redis服务器不可用");
            return;
        }
    };

    let mut lock1 = provider.get_lock("test_timeout_lock");
    let mut lock2 = provider.get_lock("test_timeout_lock");

    // 第一个锁获取成功
    assert!(lock1.try_lock().unwrap());

    // 第二个锁应该在超时内无法获取
    let start = std::time::Instant::now();
    let result = lock2.try_lock_with_timeout(Duration::from_millis(100));
    let elapsed = start.elapsed();

    assert!(!result.unwrap());
    assert!(elapsed >= Duration::from_millis(100));
    assert!(elapsed < Duration::from_millis(200)); // 允许一些误差

    // 释放第一个锁
    assert!(lock1.unlock().is_ok());
}

#[test]
fn test_key_generation() {
    // 测试Redis key生成
    let key1 = RedisKeyGenerator::generate_test_program_test_order_key(
        "customer",
        "sub_customer",
        "upload_type",
        "test_area",
        "factory",
        "factory_site",
        "device_id",
        "test_stage",
        "lot_type",
        "test_program",
    );

    assert!(key1.contains("test_program_test_order"));
    assert!(key1.contains("customer"));
    assert!(key1.contains("test_program"));

    let key2 = RedisKeyGenerator::generate_yms_die_key(
        "customer",
        "sub_customer",
        "factory",
        "factory_site",
        "test_area",
        "test_stage",
        "device_id",
        "lot_type",
        "lot_id",
        "wafer_no",
    );

    assert!(key2.contains("yms_die"));
    assert!(key2.contains("customer"));
    assert!(key2.contains("wafer_no"));
}