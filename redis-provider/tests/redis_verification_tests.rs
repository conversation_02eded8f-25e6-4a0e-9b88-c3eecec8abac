use redis_provider::{<PERSON><PERSON><PERSON><PERSON><PERSON>, RedisKeyGenerator};
use std::time::Duration;
use std::thread;
use common::model::key::{<PERSON><PERSON><PERSON>, WaferKey};
// 这些测试需要运行Redis服务器
// 可以使用 docker run -d -p 6379:6379 redis:latest 启动Redis

#[test]
fn test_redis_data_verification() {
    let provider = match RedisProvider::new("redis://127.0.0.1:6379", None) {
        Ok(p) => p,
        Err(_) => {
            println!("跳过测试 - Redis服务器不可用");
            return;
        }
    };

    // 创建测试数据
    let lot_key = LotKey::new(
        "VERIFICATION_CUSTOMER".to_string(),
        "VERIFICATION_SUB".to_string(),
        "CP".to_string(),
        "VERIFICATION_FACTORY".to_string(),
        "VERIFICATION_SITE".to_string(),
        "VERIFICATION_DEVICE".to_string(),
        "VERIFICATION_LOT".to_string(),
        "CP1".to_string(),
        "PRODUCTION".to_string(),
    );

    // 测试锁的创建和验证
    let mut lock = provider.get_test_program_test_order_lock(
        &lot_key,
        "VERIFICATION_UPLOAD",
        "VERIFICATION_PROGRAM"
    );

    // 获取锁
    assert!(lock.try_lock().unwrap());
    
    // 验证锁确实存在于Redis中
    let client = provider.client();
    let mut conn = client.get_connection().unwrap();
    
    // 检查锁键是否存在
    let lock_key = lock.key().to_string();
    let exists: bool = redis::cmd("EXISTS").arg(&lock_key).query(&mut conn).unwrap();
    assert!(exists, "锁应该存在于Redis中");
    
    // 获取锁的值
    let lock_value: String = redis::cmd("GET").arg(&lock_key).query(&mut conn).unwrap();
    assert!(!lock_value.is_empty(), "锁应该有值");
    
    println!("锁键: {}", lock_key);
    println!("锁值: {}", lock_value);
    
    // 检查TTL
    let ttl: i64 = redis::cmd("TTL").arg(&lock_key).query(&mut conn).unwrap();
    assert!(ttl > 0, "锁应该有过期时间");
    println!("锁TTL: {} 秒", ttl);
    
    // 释放锁
    lock.unlock().unwrap();
    
    // 验证锁已被删除
    let exists_after: bool = redis::cmd("EXISTS").arg(&lock_key).query(&mut conn).unwrap();
    assert!(!exists_after, "锁释放后应该不存在");
}

#[test]
fn test_redis_key_generation_verification() {
    let lot_key = LotKey::new(
        "TEST_CUSTOMER".to_string(),
        "TEST_SUB".to_string(),
        "CP".to_string(),
        "TEST_FACTORY".to_string(),
        "TEST_SITE".to_string(),
        "TEST_DEVICE".to_string(),
        "TEST_LOT".to_string(),
        "CP1".to_string(),
        "PRODUCTION".to_string(),
    );

    let wafer_key = WaferKey::new(
        "TEST_CUSTOMER".to_string(),
        "TEST_SUB".to_string(),
        "TEST_FACTORY".to_string(),
        "TEST_SITE".to_string(),
        "CP".to_string(),
        "CP1".to_string(),
        "TEST_DEVICE".to_string(),
        "PRODUCTION".to_string(),
        "TEST_LOT".to_string(),
        "1".to_string(),
    );

    // 验证生成的键格式
    let test_order_key = RedisKeyGenerator::generate_test_program_test_order_key_from_lot(
        &lot_key,
        "UPLOAD_TYPE",
        "TEST_PROGRAM"
    );
    
    assert!(test_order_key.starts_with("test_program_test_order:"));
    assert!(test_order_key.contains("TEST_CUSTOMER"));
    assert!(test_order_key.contains("TEST_PROGRAM"));
    println!("测试顺序键: {}", test_order_key);

    let test_plan_key = RedisKeyGenerator::generate_test_program_test_plan_key_from_lot(
        &lot_key,
        "UPLOAD_TYPE",
        "TEST_PROGRAM"
    );
    
    assert!(test_plan_key.starts_with("test_program_test_plan:"));
    println!("测试计划键: {}", test_plan_key);

    let yms_die_key = RedisKeyGenerator::generate_yms_die_key_from_wafer(&wafer_key);
    
    assert!(yms_die_key.starts_with("yms_die:"));
    assert!(yms_die_key.contains("TEST_LOT"));
    println!("YMS die键: {}", yms_die_key);
}

#[test]
fn test_concurrent_lock_verification() {
    let provider = match RedisProvider::new("redis://127.0.0.1:6379", None) {
        Ok(p) => p,
        Err(_) => {
            println!("跳过测试 - Redis服务器不可用");
            return;
        }
    };

    let provider = std::sync::Arc::new(provider);
    let mut handles = vec![];

    // 启动多个线程尝试获取同一个锁
    for i in 0..3 {
        let provider_clone = provider.clone();
        let handle = thread::spawn(move || {
            let mut lock = provider_clone.get_lock(&format!("concurrent_test_lock"));
            
            if lock.try_lock().unwrap_or(false) {
                println!("线程 {} 获取到锁", i);
                
                // 验证锁在Redis中存在
                let client = provider_clone.client();
                let mut conn = client.get_connection().unwrap();
                let exists: bool = redis::cmd("EXISTS").arg(lock.key()).query(&mut conn).unwrap();
                assert!(exists, "锁应该存在于Redis中");
                
                thread::sleep(Duration::from_millis(100));
                lock.unlock().unwrap();
                println!("线程 {} 释放锁", i);
                true
            } else {
                println!("线程 {} 未能获取锁", i);
                false
            }
        });
        handles.push(handle);
    }

    let mut success_count = 0;
    for handle in handles {
        if handle.join().unwrap() {
            success_count += 1;
        }
    }

    // 在并发情况下，只有一个线程应该能获取到锁
    assert_eq!(success_count, 1);
}

#[test]
fn test_redis_data_persistence_verification() {
    let provider = match RedisProvider::new("redis://127.0.0.1:6379", None) {
        Ok(p) => p,
        Err(_) => {
            println!("跳过测试 - Redis服务器不可用");
            return;
        }
    };

    let client = provider.client();
    let mut conn = client.get_connection().unwrap();

    // 测试数据持久化
    let test_key = "test:persistence:verification";
    let test_value = format!("test_data_{}", chrono::Utc::now().timestamp());

    // 存储数据
    redis::cmd("SET")
        .arg(test_key)
        .arg(&test_value)
        .arg("EX")
        .arg(10) // 10秒过期
        .execute(&mut conn);

    // 验证数据存在
    let exists: bool = redis::cmd("EXISTS").arg(test_key).query(&mut conn).unwrap();
    assert!(exists, "数据应该存在于Redis中");

    // 验证数据值
    let stored_value: String = redis::cmd("GET").arg(test_key).query(&mut conn).unwrap();
    assert_eq!(stored_value, test_value, "存储的值应该与预期一致");

    // 验证TTL
    let ttl: i64 = redis::cmd("TTL").arg(test_key).query(&mut conn).unwrap();
    assert!(ttl > 0 && ttl <= 10, "TTL应该在合理范围内");

    println!("数据验证成功:");
    println!("  键: {}", test_key);
    println!("  值: {}", stored_value);
    println!("  TTL: {} 秒", ttl);

    // 清理测试数据
    let _: i32 = redis::cmd("DEL").arg(test_key).query(&mut conn).unwrap();
}