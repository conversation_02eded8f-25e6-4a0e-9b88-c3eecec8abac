# Redis测试和验证指南

本指南介绍如何测试和验证Redis中是否存储了预期的值。

## 前置条件

### 启动Redis服务器

```bash
# 使用Docker启动Redis
docker run -d -p 6379:6379 --name redis-test redis:latest

# 或者使用本地Redis服务
redis-server
```

### 安装Redis CLI工具

```bash
# Ubuntu/Debian
sudo apt-get install redis-tools

# macOS
brew install redis

# Windows
# 下载Redis for Windows或使用WSL
```

## 测试方法

### 1. 运行自动化测试

```bash
# 运行所有Redis验证测试
cargo test --test redis_verification_tests

# 运行特定测试
cargo test test_redis_data_verification
cargo test test_concurrent_lock_verification
```

### 2. 运行验证示例

```bash
# 运行Redis验证示例
cargo run --example redis_test_verification

# 启用测试工具特性
cargo run --example redis_test_verification --features test-utils
```

### 3. 手动验证方法

#### 使用Redis CLI验证

```bash
# 连接到Redis
redis-cli

# 查看所有键
KEYS *

# 查看特定模式的键
KEYS test_program_test_order:*
KEYS test_program_test_plan:*
KEYS yms_die:*

# 查看键的值
GET "test_program_test_order:customer:sub_customer:upload_type:test_area:factory:factory_site:device:test_stage:lot_type:test_program"

# 查看键的TTL（生存时间）
TTL "your_key_name"

# 检查键是否存在
EXISTS "your_key_name"

# 查看Redis信息
INFO

# 监控Redis命令（实时查看执行的命令）
MONITOR
```

#### 使用Redis Desktop Manager或RedisInsight

1. 下载并安装Redis Desktop Manager或RedisInsight
2. 连接到Redis服务器 (localhost:6379)
3. 浏览键值对
4. 实时监控数据变化

## 验证要点

### 1. 分布式锁验证

检查以下几点：

- **锁键格式**: 确保生成的Redis键符合预期格式
- **锁值**: 锁应该有唯一的值（通常包含时间戳和进程ID）
- **TTL**: 锁应该有合理的过期时间（默认5分钟）
- **原子性**: 锁的获取和释放应该是原子操作

```bash
# 示例验证命令
redis-cli KEYS "test_program_test_order:*"
redis-cli TTL "your_lock_key"
redis-cli GET "your_lock_key"
```

### 2. 键命名规范验证

确保生成的Redis键符合以下格式：

- **测试程序测试顺序锁**: `test_program_test_order:customer:sub_customer:upload_type:test_area:factory:factory_site:device_id:test_stage:lot_type:test_program`
- **测试程序测试计划锁**: `test_program_test_plan:customer:sub_customer:upload_type:test_area:factory:factory_site:device_id:test_stage:lot_type:test_program`
- **YMS die锁**: `yms_die:customer:sub_customer:factory:factory_site:test_area:test_stage:device_id:lot_type:lot_id:wafer_no`

### 3. 数据一致性验证

- **并发安全**: 多个进程同时访问时，只有一个能获取锁
- **数据完整性**: 存储和读取的数据应该完全一致
- **过期机制**: 带TTL的数据应该在指定时间后自动删除

## 常见问题排查

### 1. 连接问题

```bash
# 测试Redis连接
redis-cli ping
# 应该返回 PONG

# 检查Redis服务状态
redis-cli info server
```

### 2. 权限问题

```bash
# 检查Redis配置
redis-cli config get requirepass

# 如果设置了密码，使用密码连接
redis-cli -a your_password
```

### 3. 内存问题

```bash
# 检查Redis内存使用
redis-cli info memory

# 查看键的数量
redis-cli dbsize

# 清空数据库（谨慎使用）
redis-cli flushdb
```

## 性能测试

### 1. 锁性能测试

```bash
# 运行并发锁测试
cargo test test_concurrent_lock_verification -- --nocapture

# 使用redis-benchmark测试基础性能
redis-benchmark -t set,get -n 10000 -q
```

### 2. 内存使用监控

```bash
# 监控Redis内存使用
redis-cli --latency-history -i 1

# 查看慢查询日志
redis-cli slowlog get 10
```

## 调试技巧

### 1. 启用Redis日志

编辑Redis配置文件，启用详细日志：

```
loglevel verbose
logfile /var/log/redis/redis-server.log
```

### 2. 使用MONITOR命令

```bash
# 实时监控所有Redis命令
redis-cli monitor
```

### 3. 使用Rust日志

在代码中添加日志：

```rust
use log::{info, debug, error};

// 在关键操作处添加日志
info!("尝试获取锁: {}", lock_key);
debug!("锁值: {}", lock_value);
```

## 自动化测试脚本

创建一个简单的测试脚本：

```bash
#!/bin/bash
# test_redis.sh

echo "启动Redis测试..."

# 检查Redis连接
if ! redis-cli ping > /dev/null 2>&1; then
    echo "错误: 无法连接到Redis服务器"
    exit 1
fi

echo "Redis连接正常"

# 运行Rust测试
cargo test --test redis_verification_tests

# 运行示例
cargo run --example redis_test_verification

echo "测试完成"
```

## 监控和告警

### 1. 设置Redis监控

使用Redis的INFO命令定期检查：

- 内存使用情况
- 连接数
- 命令执行统计
- 键空间统计

### 2. 设置告警

监控以下指标：

- Redis服务可用性
- 内存使用率超过80%
- 连接数异常
- 慢查询增多

## 最佳实践

1. **定期清理**: 设置合理的TTL，避免内存泄漏
2. **监控资源**: 定期检查Redis内存和CPU使用情况
3. **备份数据**: 对重要数据进行定期备份
4. **安全配置**: 设置密码，限制访问IP
5. **性能优化**: 根据使用模式调整Redis配置

## 示例验证流程

```rust
// 完整的验证流程示例
use redis_provider::{RedisProvider, LotKey};

fn verify_redis_operations() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 创建连接
    let provider = RedisProvider::new("redis://127.0.0.1:6379", None)?;
    
    // 2. 测试连接
    assert_eq!(provider.ping()?, "PONG");
    
    // 3. 创建测试数据
    let lot_key = LotKey::new(/* 参数 */);
    
    // 4. 获取锁
    let mut lock = provider.get_test_program_test_order_lock(&lot_key, "type", "program");
    assert!(lock.try_lock()?);
    
    // 5. 验证锁存在
    let client = provider.client();
    let mut conn = client.get_connection()?;
    let exists: bool = redis::cmd("EXISTS").arg(lock.key()).query(&mut conn)?;
    assert!(exists);
    
    // 6. 释放锁
    lock.unlock()?;
    
    // 7. 验证锁已删除
    let exists_after: bool = redis::cmd("EXISTS").arg(lock.key()).query(&mut conn)?;
    assert!(!exists_after);
    
    println!("✅ 所有验证通过");
    Ok(())
}
```

通过以上方法，你可以全面验证Redis中的数据存储和锁机制是否按预期工作。