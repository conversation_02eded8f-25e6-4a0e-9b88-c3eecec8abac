use redis_provider::{RedisProvider};
use std::time::Duration;
use common::model::key::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Redis Provider 基本使用示例");

    // 创建Redis提供者
    let provider = RedisProvider::new("redis://redis01.dev.guwave.com:6379", None)?;
    
    // 测试连接
    println!("测试Redis连接: {}", provider.ping()?);

    // 创建LotKey示例
    let lot_key = LotKey::new(
        "YEESTOR".to_string(),
        "YEESTOR_SUB".to_string(),
        "CP".to_string(),
        "LEADYO".to_string(),
        "LEADYO_SITE".to_string(),
        "YS8293ENAB".to_string(),
        "lot1".to_string(),
        "CP1".to_string(),
        "PRODUCTION".to_string(),
    );

    // 获取测试程序测试顺序锁
    println!("\n=== 测试程序测试顺序锁示例 ===");
    let mut test_order_lock = provider.get_test_program_test_order_lock(
        &lot_key,
        "UPLOAD_TYPE_1",
        "TEST_PROGRAM_1"
    );
    
    println!("锁键名: {}", test_order_lock.key());
    
    // 尝试获取锁
    match test_order_lock.try_lock() {
        Ok(true) => {
            println!("成功获取测试顺序锁");
            
            // 模拟业务操作
            std::thread::sleep(Duration::from_millis(100));
            
            // 释放锁
            test_order_lock.unlock()?;
            println!("成功释放测试顺序锁");
        }
        Ok(false) => {
            println!("获取测试顺序锁失败 - 锁已被占用");
        }
        Err(e) => {
            println!("获取测试顺序锁出错: {}", e);
        }
    }

    // 获取测试程序测试计划锁
    println!("\n=== 测试程序测试计划锁示例 ===");
    let mut test_plan_lock = provider.get_test_program_test_plan_lock(
        &lot_key,
        "UPLOAD_TYPE_1",
        "TEST_PROGRAM_1"
    );
    
    println!("锁键名: {}", test_plan_lock.key());
    
    // 使用带超时的锁获取
    match test_plan_lock.try_lock_with_timeout(Duration::from_secs(5)) {
        Ok(true) => {
            println!("成功获取测试计划锁");
            test_plan_lock.unlock()?;
            println!("成功释放测试计划锁");
        }
        Ok(false) => {
            println!("获取测试计划锁超时");
        }
        Err(e) => {
            println!("获取测试计划锁出错: {}", e);
        }
    }

    // 创建WaferKey示例
    let wafer_key = WaferKey::new(
        "YEESTOR".to_string(),
        "YEESTOR_SUB".to_string(),
        "LEADYO".to_string(),
        "LEADYO_SITE".to_string(),
        "CP".to_string(),
        "CP1".to_string(),
        "YS8293ENAB".to_string(),
        "PRODUCTION".to_string(),
        "ENF083".to_string(),
        "25".to_string(),
    );

    // 获取YMS die锁
    println!("\n=== YMS Die锁示例 ===");
    let mut yms_die_lock = provider.get_yms_die_lock(&wafer_key);
    
    println!("锁键名: {}", yms_die_lock.key());
    
    // 使用阻塞锁获取
    match yms_die_lock.lock() {
        Ok(()) => {
            println!("成功获取YMS die锁");
            
            // 模拟业务操作
            std::thread::sleep(Duration::from_millis(100));
            
            yms_die_lock.unlock()?;
            println!("成功释放YMS die锁");
        }
        Err(e) => {
            println!("获取YMS die锁出错: {}", e);
        }
    }

    // 演示自定义锁
    println!("\n=== 自定义锁示例 ===");
    let mut custom_lock = provider.get_lock("custom:lock:key");
    
    if custom_lock.try_lock()? {
        println!("成功获取自定义锁");
        custom_lock.unlock()?;
        println!("成功释放自定义锁");
    }

    println!("\n示例执行完成！");
    Ok(())
}