use redis_provider::{<PERSON><PERSON><PERSON><PERSON><PERSON>, RedisKeyGenerator};
use std::time::Duration;
use common::model::key::{<PERSON><PERSON><PERSON>, <PERSON>afer<PERSON><PERSON>};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Redis数据验证测试 ===");

    // 创建Redis提供者
    let provider = RedisProvider::new("redis://redis01.dev.guwave.com:6379", Some("devops@guwave"))?;
    
    // 测试连接
    println!("测试Redis连接: {}", provider.ping()?);

    // 创建测试数据
    let lot_key = LotKey::new(
        "TEST_CUSTOMER".to_string(),
        "TEST_SUB_CUSTOMER".to_string(),
        "CP".to_string(),
        "TEST_FACTORY".to_string(),
        "TEST_SITE".to_string(),
        "TEST_DEVICE".to_string(),
        "TEST_LOT".to_string(),
        "CP1".to_string(),
        "PRODUCTION".to_string(),
    );

    let wafer_key = WaferKey::new(
        "TEST_CUSTOMER".to_string(),
        "TEST_SUB_CUSTOMER".to_string(),
        "TEST_FACTORY".to_string(),
        "TEST_SITE".to_string(),
        "CP".to_string(),
        "CP1".to_string(),
        "TEST_DEVICE".to_string(),
        "PRODUCTION".to_string(),
        "TEST_LOT_001".to_string(),
        "1".to_string(),
    );

    println!("\n=== 测试分布式锁 ===");

    // 1. 测试LotKey相关的锁
    println!("1. 测试程序测试顺序锁");
    let test_order_key = RedisKeyGenerator::generate_test_program_test_order_key_from_lot(
        &lot_key,
        "UPLOAD_TYPE_TEST",
        "TEST_PROGRAM_001"
    );
    println!("生成的Redis键: {}", test_order_key);

    let mut test_order_lock = provider.get_test_program_test_order_lock(
        &lot_key,
        "UPLOAD_TYPE_TEST",
        "TEST_PROGRAM_001"
    );

    if test_order_lock.try_lock()? {
        println!("✅ 成功获取测试顺序锁");
        
        // 模拟业务操作
        println!("执行业务逻辑...");
        std::thread::sleep(Duration::from_millis(100));
        
        test_order_lock.unlock()?;
        println!("✅ 成功释放测试顺序锁");
    } else {
        println!("❌ 获取测试顺序锁失败");
    }

    // 2. 测试程序测试计划锁
    println!("\n2. 测试程序测试计划锁");
    let test_plan_key = RedisKeyGenerator::generate_test_program_test_plan_key_from_lot(
        &lot_key,
        "UPLOAD_TYPE_TEST",
        "TEST_PROGRAM_001"
    );
    println!("生成的Redis键: {}", test_plan_key);

    let mut test_plan_lock = provider.get_test_program_test_plan_lock(
        &lot_key,
        "UPLOAD_TYPE_TEST",
        "TEST_PROGRAM_001"
    );

    if test_plan_lock.try_lock()? {
        println!("✅ 成功获取测试计划锁");
        test_plan_lock.unlock()?;
        println!("✅ 成功释放测试计划锁");
    }

    // 3. 测试WaferKey相关的锁
    println!("\n3. YMS Die锁");
    let yms_die_key = RedisKeyGenerator::generate_yms_die_key_from_wafer(&wafer_key);
    println!("生成的Redis键: {}", yms_die_key);

    let mut yms_die_lock = provider.get_yms_die_lock(&wafer_key);
    
    if yms_die_lock.try_lock()? {
        println!("✅ 成功获取YMS die锁");
        yms_die_lock.unlock()?;
        println!("✅ 成功释放YMS die锁");
    }

    println!("\n=== 测试自定义数据存储 ===");

    // 4. 测试存储和读取自定义数据
    let client = provider.client();
    let mut conn = client.get_connection()?;

    // 存储一些测试数据
    let test_data_key = "test:data:verification";
    let test_data_value = format!("测试数据_{}", chrono::Utc::now().timestamp());
    
    redis::cmd("SET")
        .arg(test_data_key)
        .arg(&test_data_value)
        .arg("EX")
        .arg(300) // 5分钟过期
        .execute(&mut conn);
    
    println!("存储测试数据: {} = {}", test_data_key, test_data_value);

    // 读取数据验证
    let stored_value: String = redis::cmd("GET")
        .arg(test_data_key)
        .query(&mut conn)?;
    
    println!("读取到的数据: {}", stored_value);
    
    if stored_value == test_data_value {
        println!("✅ 数据存储和读取验证成功");
    } else {
        println!("❌ 数据验证失败");
    }

    println!("\n=== 验证完成 ===");
    println!("你可以使用以下Redis CLI命令来手动验证:");
    println!("redis-cli");
    println!("KEYS *test*");
    println!("GET {}", test_data_key);
    println!("TTL {}", test_data_key);

    Ok(())
}