use thiserror::Error;

/// Errors that can occur when using the ClickHouse provider
#[derive(Debug, Error)]
pub enum CkProviderError {
    #[error("数据库连接错误: {0}")]
    ConnectionError(#[from] clickhouse::error::Error),
    #[error("SQL执行错误: {0}")]
    ExecutionError(String),
    #[error("数据解析错误: {0}")]
    ParseError(String),

    // Async streaming specific errors
    #[error("流已关闭: {0}")]
    StreamClosed(String),
    #[error("背压超时: {0}")]
    BackpressureTimeout(String),
    #[error("连接池已耗尽")]
    ConnectionPoolExhausted,
    #[error("流配置错误: {0}")]
    StreamConfigError(String),
    #[error("批处理错误: {0}")]
    BatchError(String),
    #[error("通道发送错误: {0}")]
    ChannelSendError(String),
    #[error("通道接收错误: {0}")]
    ChannelReceiveError(String),
}
