use crate::error::Ck<PERSON>roviderError;
use std::time::Duration;

/// Configuration for ClickHouse provider
#[derive(Clone, Debug)]
pub struct CkConfig {
    pub url: String,
    pub username: String,
    pub password: String,
    pub database: String,
    pub timeout: Duration,
    pub batch_size: usize,
    pub compression: bool,

    // Async streaming specific configuration
    pub stream_buffer_size: usize,
    pub stream_batch_size: usize,
    pub stream_flush_interval: Duration,
    pub stream_max_retries: u32,
    pub connection_pool_size: usize,
    pub backpressure_timeout: Duration,
}

impl Default for CkConfig {
    fn default() -> Self {
        Self {
            url: "http://localhost:8123".to_string(),
            username: "default".to_string(),
            password: "".to_string(),
            database: "default".to_string(),
            timeout: Duration::from_secs(30),
            batch_size: 1000,
            compression: true,

            // Default values for async streaming
            stream_buffer_size: 1000,
            stream_batch_size: 500,
            stream_flush_interval: Duration::from_millis(100),
            stream_max_retries: 3,
            connection_pool_size: 10,
            backpressure_timeout: Duration::from_secs(30),
        }
    }
}

impl CkConfig {
    /// Validate the configuration and return errors if invalid
    pub fn validate(&self) -> Result<(), CkProviderError> {
        // Validate basic configuration
        if self.url.is_empty() {
            return Err(CkProviderError::StreamConfigError("URL cannot be empty".to_string()));
        }

        if self.database.is_empty() {
            return Err(CkProviderError::StreamConfigError("Database cannot be empty".to_string()));
        }

        if self.batch_size == 0 {
            return Err(CkProviderError::StreamConfigError("Batch size must be greater than 0".to_string()));
        }

        // Validate streaming-specific configuration
        if self.stream_buffer_size == 0 {
            return Err(CkProviderError::StreamConfigError("Stream buffer size must be greater than 0".to_string()));
        }

        if self.stream_batch_size == 0 {
            return Err(CkProviderError::StreamConfigError("Stream batch size must be greater than 0".to_string()));
        }

        if self.stream_batch_size > self.stream_buffer_size {
            return Err(CkProviderError::StreamConfigError(
                "Stream batch size cannot be larger than buffer size".to_string(),
            ));
        }

        if self.connection_pool_size == 0 {
            return Err(CkProviderError::StreamConfigError("Connection pool size must be greater than 0".to_string()));
        }

        if self.stream_flush_interval.is_zero() {
            return Err(CkProviderError::StreamConfigError("Stream flush interval must be greater than 0".to_string()));
        }

        if self.backpressure_timeout.is_zero() {
            return Err(CkProviderError::StreamConfigError("Backpressure timeout must be greater than 0".to_string()));
        }

        Ok(())
    }

    /// Create a new configuration with streaming defaults optimized for the given use case
    pub fn with_streaming_defaults() -> Self {
        Self::default()
    }

    /// Builder method to set stream buffer size
    pub fn with_stream_buffer_size(mut self, size: usize) -> Self {
        self.stream_buffer_size = size;
        self
    }

    /// Builder method to set stream batch size
    pub fn with_stream_batch_size(mut self, size: usize) -> Self {
        self.stream_batch_size = size;
        self
    }

    /// Builder method to set stream flush interval
    pub fn with_stream_flush_interval(mut self, interval: Duration) -> Self {
        self.stream_flush_interval = interval;
        self
    }

    /// Builder method to set connection pool size
    pub fn with_connection_pool_size(mut self, size: usize) -> Self {
        self.connection_pool_size = size;
        self
    }

    /// Builder method to set backpressure timeout
    pub fn with_backpressure_timeout(mut self, timeout: Duration) -> Self {
        self.backpressure_timeout = timeout;
        self
    }

}

/// Configuration specific to streaming operations
#[derive(Clone, Debug)]
pub struct StreamConfig {
    pub buffer_size: usize,
    pub batch_size: usize,
    pub flush_interval: Duration,
    pub max_retries: u32,
    pub connection_pool_size: usize,
    pub backpressure_timeout: Duration,
    pub enable_parallel_flush: bool,
    pub max_concurrent_flushes: usize,
}

impl Default for StreamConfig {
    fn default() -> Self {
        Self {
            buffer_size: 1000,
            batch_size: 500,
            flush_interval: Duration::from_millis(100),
            max_retries: 3,
            connection_pool_size: 10,
            backpressure_timeout: Duration::from_secs(30),
            enable_parallel_flush: false,
            max_concurrent_flushes: 1,
        }
    }
}

impl StreamConfig {
    /// Create StreamConfig from CkConfig
    pub fn from_ck_config(config: &CkConfig) -> Self {
        Self {
            buffer_size: config.stream_buffer_size,
            batch_size: config.stream_batch_size,
            flush_interval: config.stream_flush_interval,
            max_retries: config.stream_max_retries,
            connection_pool_size: config.connection_pool_size,
            backpressure_timeout: config.backpressure_timeout,
            enable_parallel_flush: true,
            max_concurrent_flushes: 4,
        }
    }

    /// Validate the stream configuration
    pub fn validate(&self) -> Result<(), CkProviderError> {
        if self.buffer_size == 0 {
            return Err(CkProviderError::StreamConfigError("Buffer size must be greater than 0".to_string()));
        }

        if self.batch_size == 0 {
            return Err(CkProviderError::StreamConfigError("Batch size must be greater than 0".to_string()));
        }

        if self.batch_size > self.buffer_size {
            return Err(CkProviderError::StreamConfigError("Batch size cannot be larger than buffer size".to_string()));
        }

        if self.connection_pool_size == 0 {
            return Err(CkProviderError::StreamConfigError("Connection pool size must be greater than 0".to_string()));
        }

        if self.flush_interval.is_zero() {
            return Err(CkProviderError::StreamConfigError("Flush interval must be greater than 0".to_string()));
        }

        if self.backpressure_timeout.is_zero() {
            return Err(CkProviderError::StreamConfigError("Backpressure timeout must be greater than 0".to_string()));
        }

        Ok(())
    }

    /// Builder method to set buffer size
    pub fn with_buffer_size(mut self, size: usize) -> Self {
        self.buffer_size = size;
        self
    }

    /// Builder method to set batch size
    pub fn with_batch_size(mut self, size: usize) -> Self {
        self.batch_size = size;
        self
    }

    /// Builder method to set flush interval
    pub fn with_flush_interval(mut self, interval: Duration) -> Self {
        self.flush_interval = interval;
        self
    }

    /// Builder method to set max retries
    pub fn with_max_retries(mut self, retries: u32) -> Self {
        self.max_retries = retries;
        self
    }

    /// Builder method to set connection pool size
    pub fn with_connection_pool_size(mut self, size: usize) -> Self {
        self.connection_pool_size = size;
        self
    }

    /// Builder method to set backpressure timeout
    pub fn with_backpressure_timeout(mut self, timeout: Duration) -> Self {
        self.backpressure_timeout = timeout;
        self
    }

    /// Builder method to enable/disable parallel flush
    pub fn with_parallel_flush(mut self, enable: bool) -> Self {
        self.enable_parallel_flush = enable;
        self
    }

    /// Builder method to set max concurrent flushes
    pub fn with_max_concurrent_flushes(mut self, max: usize) -> Self {
        self.max_concurrent_flushes = max;
        self
    }
}
