# 并行 Flush 功能

## 概述

CkStreamProcessor 现在支持并行 flush 功能，可以显著提高大批量数据写入 ClickHouse 的性能。

## 工作原理

### 串行 Flush (默认行为)
- 每次只能处理一个批次
- 必须等待当前批次完成才能处理下一个
- 简单可靠，适合小到中等数据量

### 并行 Flush
- 多个批次可以同时写入 ClickHouse
- 使用 Semaphore 控制并发数量，避免资源耗尽
- 每个批次在独立的 tokio 任务中处理
- 适合大批量数据写入场景

## 配置选项

```rust
let stream_config = StreamConfig::default()
    .with_parallel_flush(true)           // 启用并行flush
    .with_max_concurrent_flushes(4);     // 最多4个并发flush
```

### 配置参数说明

- `enable_parallel_flush`: 是否启用并行flush (默认: true)
- `max_concurrent_flushes`: 最大并发flush数量 (默认: 4)

## 使用示例

```rust
use ck_provider::{StreamConfig, CkStreamProcessorBuilder};
use std::time::Duration;

// 高性能配置
let config = StreamConfig::default()
    .with_buffer_size(2000)
    .with_batch_size(500)
    .with_flush_interval(Duration::from_secs(1))
    .with_parallel_flush(true)
    .with_max_concurrent_flushes(6);

let mut processor = CkStreamProcessorBuilder::new()
    .with_config(config)
    // ... 其他配置
    .build()?;
```

## 性能对比

### 串行 Flush
- **优势**: 简单可靠，内存使用稳定
- **劣势**: 吞吐量受限于单个批次的写入速度
- **适用场景**: 小到中等数据量，对延迟敏感的场景

### 并行 Flush  
- **优势**: 高吞吐量，充分利用 ClickHouse 的并发能力
- **劣势**: 内存使用较高，错误处理更复杂
- **适用场景**: 大批量数据写入，对吞吐量要求高的场景

## 监控和调试

### 监控指标

```rust
// 获取当前pending的flush数量
let pending = processor.pending_flushes();
println!("Pending flushes: {}", pending);

// 检查是否有pending的flush
if processor.has_pending_flushes() {
    println!("还有flush操作在进行中...");
}
```

### 日志输出

并行flush会产生以下日志：
- `Parallel flushing batch of N items` - 开始并行flush
- `Parallel batch write succeeded for N items` - 并行写入成功
- `Parallel batch write failed` - 并行写入失败
- `All pending flushes completed` - 所有pending flush完成

## 最佳实践

### 1. 合理设置并发数
```rust
// 根据系统资源调整并发数
let max_concurrent = std::cmp::min(
    num_cpus::get() * 2,  // CPU核心数的2倍
    8                     // 但不超过8个
);

let config = StreamConfig::default()
    .with_max_concurrent_flushes(max_concurrent);
```

### 2. 监控内存使用
```rust
// 较大的批次大小配合并行flush
let config = StreamConfig::default()
    .with_batch_size(1000)           // 较大批次
    .with_max_concurrent_flushes(4)  // 适中的并发数
    .with_buffer_size(5000);         // 足够的缓冲区
```

### 3. 错误处理
```rust
// 启用重试机制
let config = StreamConfig::default()
    .with_max_retries(5)             // 增加重试次数
    .with_parallel_flush(true);
```

### 4. 优雅关闭
```rust
// 发送结束信号后等待所有flush完成
sender.send(None).await?;
processor_handle.await?;  // 这会等待所有pending flush完成
```

## 注意事项

1. **内存使用**: 并行flush会同时缓存多个批次，增加内存使用
2. **连接数**: 虽然使用同一个provider，但并发写入可能增加连接压力
3. **错误处理**: 部分批次失败不会影响其他批次，但需要监控错误指标
4. **顺序保证**: 并行flush不保证写入顺序，如果需要顺序请使用串行模式

## 故障排除

### 常见问题

1. **内存不足**
   - 减少 `max_concurrent_flushes`
   - 减少 `batch_size`
   - 增加 `flush_interval` 频率

2. **连接超时**
   - 检查 ClickHouse 连接配置
   - 增加 `backpressure_timeout`
   - 减少并发数

3. **性能不如预期**
   - 检查 ClickHouse 服务器性能
   - 调整批次大小和并发数的平衡
   - 监控网络带宽使用情况