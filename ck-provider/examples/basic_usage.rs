use ck_provider::{write_to_ck_parallel, CkConfig, Ck<PERSON>rovider, CkProviderExt, CkProviderImpl};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::time::Duration;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[derive(Row, Serialize, Deserialize, Debug, Clone)]
struct User {
    id: u32,
    name: String,
    age: u8,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .with_line_number(true)
        .init();

    // 配置ClickHouse连接 - 使用默认配置并自定义需要的字段
    let config = CkConfig {
        url: "http://localhost:8123".to_string(),
        username: "default".to_string(),
        password: "".to_string(),
        database: "default".to_string(),
        timeout: Duration::from_secs(30),
        batch_size: 1000,
        compression: true,
        ..Default::default() // 使用默认值填充其他字段
    };

    // 创建ClickHouse Provider
    let provider = CkProviderImpl::new(config);

    // 创建测试表
    provider.execute("DROP TABLE IF EXISTS users").await?;
    provider
        .execute("CREATE TABLE users (id UInt32, name String, age UInt8) ENGINE = Memory")
        .await?;

    println!("创建表成功");

    // 准备插入的数据
    let users = vec![
        User { id: 1, name: "张三".to_string(), age: 25 },
        User { id: 2, name: "李四".to_string(), age: 30 },
        User { id: 3, name: "王五".to_string(), age: 35 },
    ];

    // 插入数据
    provider.insert("users", &users).await?;
    println!("插入数据成功");

    // 查询数据
    let result: Vec<User> = provider.query("SELECT * FROM users").await?;
    println!("查询结果: {:?}", result);

    // 使用迭代器查询
    let mut iter = provider.query_iter::<User>("SELECT * FROM users WHERE age > 25").await?;
    println!("迭代查询结果:");
    while let Some(user) = iter.next().await? {
        println!("  {}: {}, {}", user.id, user.name, user.age);
    }

    // 执行计数查询
    let count = provider.count("SELECT COUNT(*) FROM users").await?;
    println!("用户总数: {:?}", count);

    // 演示并行写入
    let more_users = vec![
        User { id: 4, name: "赵六".to_string(), age: 40 },
        User { id: 5, name: "钱七".to_string(), age: 45 },
        User { id: 6, name: "孙八".to_string(), age: 50 },
    ];

    write_to_ck_parallel(&provider, "users", &more_users, 2).await?;
    println!("并行写入成功");

    // 查询所有数据
    let all_users: Vec<User> = provider.query("SELECT * FROM users ORDER BY id").await?;
    println!("所有用户: {:?}", all_users);

    // 清理
    provider.execute("DROP TABLE IF EXISTS users").await?;
    println!("清理完成");

    Ok(())
}
