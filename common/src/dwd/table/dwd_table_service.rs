use crate::dto::ods::test_item_data_parquet::TestItemDataParquet;
use crate::model::constant::file_category::FileCategory;
use crate::model::constant::test_result::TestResult;
use crate::model::constant::test_state::TestState;
use crate::utils::hex;

pub struct DwdTableService;

impl DwdTableService {
    /// Build TestItem from testNum and testTxt
    ///
    /// Corresponds to: DwdTableService.scala:570
    /// def buildTestItem(testNum: java.lang.Long, testTxt: String): String
    pub fn build_test_item(test_num: Option<i64>, test_txt: &str) -> String {
        if let Some(num) = test_num {
            format!("{}:{}", num, test_txt)
        } else {
            test_txt.to_string()
        }
    }

    /// Build bin string (HBIN/SBIN)
    ///
    /// Corresponds to: DwdTableService.scala:577-579
    /// def buildBin(prefix: String, binNum: java.lang.Long, binNam: String): String
    pub fn build_bin(prefix: &str, bin_num: Option<i64>, bin_nam: Option<&str>) -> String {
        if let Some(num) = bin_num {
            format!("{}{}-{}", prefix, num, bin_nam.unwrap_or(""))
        } else {
            String::new()
        }
    }

    /// Calculate test state
    ///
    /// Corresponds to: DwdTableService.scala:101-104
    /// def calculateTestState(testItemData: TestItemData, fileCategory: String): String
    pub fn calculate_test_state_file_category(test_item_data: &TestItemDataParquet, file_category: &str) -> String {
        if FileCategory::RAW_DATA.to_str() == file_category {
            TestState::Invalid.to_string()
        } else {
            if test_item_data.testitemType.as_deref() == Some("F") {
                Self::calculate_test_state(test_item_data.testFlg.as_deref().unwrap())
            } else {
                Self::calculate_test_state_parm_flg(
                    test_item_data.testFlg.as_deref().unwrap(),
                    test_item_data.parmFlg.as_deref().unwrap(),
                )
            }
        }
    }

    pub fn calculate_test_state(test_flg_str: &str) -> String {
        let test_flg = hex::hex_to_byte(test_flg_str);
        if test_flg.starts_with(&['0', '0', '0', '0', '0', '0', '0']) {
            TestState::Valid.to_string()
        } else {
            TestState::Invalid.to_string()
        }
    }

    pub fn calculate_test_state_parm_flg(test_flg_str: &str, parm_flg_str: &str) -> String {
        let test_flg = hex::hex_to_byte(test_flg_str);
        let parm_flg = hex::hex_to_byte(parm_flg_str);
        // 测试结果result是否可信
        if test_flg.starts_with(&['0', '0', '0', '0', '0', '0']) && parm_flg.starts_with(&['0', '0', '0']) {
            // 文档表示testFlg前六位都为0且parmFlg前3位都位0的情况下result才有效
            TestState::Valid.to_string()
        } else {
            TestState::Invalid.to_string()
        }
    }

    /// Calculate test value
    ///
    /// Corresponds to: DwdTableService.scala:142-150
    /// def calculateTestValue(testItemData: TestItemData, fileCategory: String, needMultiplyScale: Boolean): BigDecimal
    pub fn calculate_test_value(
        test_item_data: &TestItemDataParquet,
        file_category: &str,
        need_multiply_scale: bool,
    ) -> Option<f64> {
        if FileCategory::RAW_DATA.to_str() == file_category {
            test_item_data.testValue.map(|v| v as f64)
        } else if test_item_data.result.is_some()
            && (test_item_data.result.unwrap().is_infinite() || test_item_data.result.unwrap().is_nan())
        {
            Some(f64::MAX)
        } else {
            if test_item_data.testitemType.as_deref() == Some("F") {
                Self::calculate_test_result_file_category(test_item_data, file_category).map(|v| v as f64)
            } else {
                Self::calculate_with_scale(test_item_data, need_multiply_scale)
            }
        }
    }

    pub fn calculate_with_scale(test_item_data: &TestItemDataParquet, need_multiply_scale: bool) -> Option<f64> {
        let opt_flg = hex::hex_to_byte(test_item_data.optFlg.as_deref().unwrap());
        let mut flag = true;
        for index in 0..opt_flg.len() {
            if opt_flg[index] == '1' {
                flag = false;
            }
        }
        if test_item_data.result.is_none() {
            return None;
        }
        if flag && need_multiply_scale {
            Some(test_item_data.result.unwrap() * 10f64.powi(test_item_data.resScal.unwrap()))
        } else {
            Some(test_item_data.result.unwrap())
        }
    }

    /// Calculate test result
    ///
    /// Corresponds to: DwdTableService.scala:153-161
    /// def calculateTestResult(testItemData: TestItemData, fileCategory: String): Integer
    pub fn calculate_test_result_file_category(
        test_item_data: &TestItemDataParquet,
        file_category: &str,
    ) -> Option<i32> {
        if FileCategory::RAW_DATA.to_str() == file_category {
            test_item_data.testResult
        } else if test_item_data.result.is_some()
            && (test_item_data.result.unwrap().is_infinite() || test_item_data.result.unwrap().is_nan())
        {
            // Infinite值处理,testResult为2
            Some(2)
        } else {
            if test_item_data.testitemType.as_deref() == Some("F") {
                Self::calculate_test_result(test_item_data.testFlg.as_deref().unwrap())
            } else {
                Self::calculate_test_result_parm_flg(
                    test_item_data.testFlg.as_deref().unwrap(),
                    test_item_data.parmFlg.as_deref().unwrap(),
                )
            }
        }
    }

    pub fn calculate_test_result(test_flg_str: &str) -> Option<i32> {
        let test_flg = hex::hex_to_byte(test_flg_str);
        if test_flg.starts_with(&['0', '0', '0', '0', '0', '0', '0', '0']) {
            Some(1)
        } else {
            Some(0)
        }
    }

    pub fn calculate_test_result_parm_flg(test_flg_str: &str, parm_flg_str: &str) -> Option<i32> {
        let test_flg = hex::hex_to_byte(test_flg_str);
        let parm_flg = hex::hex_to_byte(parm_flg_str);
        let test_result =
            if test_flg.starts_with(&['0', '0', '0', '0', '0', '0']) && parm_flg.starts_with(&['0', '0', '0']) {
                if test_flg[7] == '0' {
                    TestResult::P
                } else {
                    TestResult::F
                }
            } else {
                TestResult::F
            };
        if test_result == TestResult::F {
            Some(0)
        } else {
            Some(1)
        }
    }
    /// Calculate res_scal
    ///
    /// Corresponds to: DwdTableService.scala:186-192
    /// def calculateResScal(testItemData: TestItemData, fileCategory: String): Integer
    pub fn calculate_res_scal(test_item_data: &TestItemDataParquet, file_category: &str) -> Option<i32> {
        if FileCategory::RAW_DATA.to_str() == file_category {
            test_item_data.resScal
        } else {
            if test_item_data.testitemType.as_deref() == Some("F") {
                None
            } else {
                Self::calculate_limit_scal(
                    test_item_data.optFlg.as_deref().unwrap(),
                    &[0],
                    test_item_data.resScal.unwrap(),
                )
            }
        }
    }

    pub fn calculate_limit_scal(opt_flg: &str, limit_scal: &[i32], res_scal: i32) -> Option<i32> {
        let opt_flg = hex::hex_to_byte(opt_flg);
        let invalid_flag = limit_scal.iter().any(|index| opt_flg[*index as usize] == '1');
        if invalid_flag {
            None
        } else {
            Some(res_scal)
        }
    }

    /// Calculate llm_scal
    ///
    /// Corresponds to: DwdTableService.scala:175-184
    /// def calculateLlmScal(testItemData: TestItemData, fileCategory: String): Integer
    pub fn calculate_llm_scal(test_item_data: &TestItemDataParquet, file_category: &str) -> Option<i32> {
        if FileCategory::RAW_DATA.to_str() == file_category {
            test_item_data.llmScal
        } else {
            if test_item_data.testitemType.as_deref() == Some("F") {
                None
            } else {
                Self::calculate_limit_scal(
                    test_item_data.optFlg.as_deref().unwrap(),
                    &[4, 6],
                    test_item_data.llmScal.unwrap(),
                )
            }
        }
    }

    /// Calculate hlm_scal
    ///
    /// Corresponds to: DwdTableService.scala:195-203
    /// def calculateHlmScal(testItemData: TestItemData, fileCategory: String): Integer
    pub fn calculate_hlm_scal(test_item_data: &TestItemDataParquet, file_category: &str) -> Option<i32> {
        if FileCategory::RAW_DATA.to_str() == file_category {
            test_item_data.hlmScal
        } else {
            if test_item_data.testitemType.as_deref() == Some("F") {
                None
            } else {
                Self::calculate_limit_scal(
                    test_item_data.optFlg.as_deref().unwrap(),
                    &[5, 7],
                    test_item_data.hlmScal.unwrap(),
                )
            }
        }
    }

    /// Calculate lo_limit
    ///
    /// Corresponds to: DwdTableService.scala:206-214
    /// def calculateLoLimit(testItemData: TestItemData, fileCategory: String, needMultiplyScale: Boolean): BigDecimal
    pub fn calculate_lo_limit(
        test_item_data: &TestItemDataParquet,
        file_category: &str,
        need_multiply_scale: bool,
    ) -> Option<f64> {
        if FileCategory::RAW_DATA.to_str() == file_category {
            test_item_data.loLimit.map(|v| v as f64)
        } else {
            if test_item_data.testitemType.as_deref() == Some("F") {
                None
            } else {
                Self::calculate_limit(
                    test_item_data.optFlg.as_deref().unwrap(),
                    &[4, 6],
                    test_item_data.loLimit,
                    test_item_data.llmScal.unwrap(),
                    need_multiply_scale,
                )
            }
        }
    }

    pub fn calculate_limit(
        opt_flg: &str,
        indexes: &[i32],
        data: Option<f64>,
        scale: i32,
        need_multiply_scale: bool,
    ) -> Option<f64> {
        let opt_flg = hex::hex_to_byte(opt_flg);
        let invalid_flag = indexes.iter().any(|index| opt_flg[*index as usize] == '1');
        if data.is_none() || invalid_flag {
            None
        } else {
            if need_multiply_scale {
                // BigDecimal.valueOf(data).multiply(BigDecimal.valueOf(Math.pow(10, scale)))
                Some(data.unwrap() * 10f64.powi(scale))
            } else {
                Some(data.unwrap())
            }
        }
    }

    /// Calculate hi_limit
    ///
    /// Corresponds to: DwdTableService.scala:217-225
    /// def calculateHiLimit(testItemData: TestItemData, fileCategory: String, needMultiplyScale: Boolean): BigDecimal
    pub fn calculate_hi_limit(
        test_item_data: &TestItemDataParquet,
        file_category: &str,
        need_multiply_scale: bool,
    ) -> Option<f64> {
        if FileCategory::RAW_DATA.to_str() == file_category {
            test_item_data.hiLimit.map(|v| v as f64)
        } else {
            if test_item_data.testitemType.as_deref() == Some("F") {
                None
            } else {
                Self::calculate_limit(
                    test_item_data.optFlg.as_deref().unwrap(),
                    &[5, 7],
                    test_item_data.hiLimit,
                    test_item_data.hlmScal.unwrap(),
                    need_multiply_scale,
                )
            }
        }
    }
}
