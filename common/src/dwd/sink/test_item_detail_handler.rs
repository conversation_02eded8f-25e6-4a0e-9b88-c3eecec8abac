use crate::ck::ck_sink::Sink<PERSON><PERSON><PERSON>;
use crate::dto::dwd::test_item_detail_row::TestItemDetailRow;
use crate::model::constant::{CLUSTER_NAME, CLUSTER_TABLE, LOCAL_TABLE};

const TABLE_NAME: &str = "dwd_test_item_detail{CLUSTER}";
const PARTITION_EXPR: &str =
    "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}', {LOT_BUCKET})";

#[derive(Debug, <PERSON>lone)]
pub struct TestItemDetailHandler {
    pub db_name: String,
    pub table_name: String,
}

impl TestItemDetailHandler {
    pub fn new(db_name: String, insert_cluster_table: bool) -> Self {
        let table_name = if insert_cluster_table {
            TABLE_NAME.replace(CLUSTER_NAME, CLUSTER_TABLE)
        } else {
            TABLE_NAME.replace(CLUSTER_NAME, LOCAL_TABLE)
        };
        Self { db_name, table_name }
    }
}

impl SinkHandler<TestItemDetailRow> for TestItemDetailHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }
    fn partition_expr(&self) -> &str {
        PARTITION_EXPR
    }
}
