use crate::dto::dwd::die_detail_parquet::DieDetailParquet;
use crate::dwd::model::value::die_test_info::DieTestInfo;
use crate::model::key::die_key::<PERSON><PERSON><PERSON>;
use std::collections::HashMap;
use std::error::Error;

pub struct DwdService;

impl DwdService {
    pub fn build_die_test_info_broadcast_map(
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<DieKey, DieTestInfo>, Box<dyn Error>> {
        let mut die_test_info_map = HashMap::new();

        for die_detail in die_details {
            // Create DieKey from FILE_ID and C_PART_ID (corresponds to Die<PERSON><PERSON> in Scala)
            let die_key = DieKey { file_id: die_detail.FILE_ID.unwrap(), c_part_id: die_detail.C_PART_ID.unwrap() };
            // 处理ecid_extra逻辑
            let ecid_extra = match &die_detail.ECID_EXTRA {
                None => {
                    // 如果ECID_EXTRA为空，检查ECID_EXT
                    if let Some(ecid_ext) = &die_detail.ECID_EXT {
                        if !ecid_ext.is_empty() {
                            // 创建一个只有一个键值对的HashMap
                            let mut extra_map = HashMap::new();
                            extra_map.insert("ECID1".to_string(), ecid_ext.clone());
                            Some(extra_map)
                        } else {
                            Some(HashMap::new())
                        }
                    } else {
                        Some(HashMap::new())
                    }
                }
                Some(extra) => Some(extra.clone()),
            };

            // Convert DieDetailParquet to DieTestInfo (corresponds to buildDieTestInfo in Scala)
            let die_test_info = DieTestInfo {
                wafer_lot_id: die_detail.WAFER_LOT_ID.clone(),
                wafer_id: die_detail.WAFER_ID.clone(),
                wafer_no: die_detail.WAFER_NO.clone(),
                is_first_test: die_detail.IS_FIRST_TEST,
                is_final_test: die_detail.IS_FINAL_TEST,
                is_first_test_ignore_tp: die_detail.IS_FIRST_TEST_IGNORE_TP,
                is_final_test_ignore_tp: die_detail.IS_FINAL_TEST_IGNORE_TP,
                max_offline_retest: die_detail.MAX_OFFLINE_RETEST,
                max_online_retest: die_detail.MAX_ONLINE_RETEST,
                is_dup_first_test: die_detail.IS_DUP_FIRST_TEST,
                is_dup_final_test: die_detail.IS_DUP_FINAL_TEST,
                is_dup_first_test_ignore_tp: die_detail.IS_DUP_FIRST_TEST_IGNORE_TP,
                is_dup_final_test_ignore_tp: die_detail.IS_DUP_FINAL_TEST_IGNORE_TP,
                x_coord: die_detail.X_COORD,
                y_coord: die_detail.Y_COORD,
                ecid: die_detail.ECID.clone(),
                is_standard_ecid: die_detail.IS_STANDARD_ECID,
                uid: die_detail.UID.clone(),
                chip_id: die_detail.CHIP_ID.clone(),
                ecid_extra,
                efuse_extra: die_detail.EFUSE_EXTRA.clone(),
                test_program: die_detail.TEST_PROGRAM.clone(),
            };

            // Insert into map (Scala uses distinct().collect.toMap, so we handle duplicates by overwriting)
            die_test_info_map.insert(die_key, die_test_info);
        }

        Ok(die_test_info_map)
    }
}
