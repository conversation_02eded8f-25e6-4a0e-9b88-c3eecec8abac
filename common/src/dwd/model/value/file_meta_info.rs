use serde::{Deserialize, Serialize};

// Corresponds to Scala file:
// dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/model/value/FileMetaInfo.scala

/// File-level metadata information extracted from DieDetail
///
/// Corresponds to: FileMetaInfo.scala
/// case class FileMetaInfo(var CUSTOMER: String,
///                         var SUB_CUSTOMER: String,
///                         var UPLOAD_TYPE: String,
///                         var FILE_ID: java.lang.Long,
///                         var FILE_NAME: String,
///                         var FILE_TYPE: String,
///                         var DEVICE_ID: String,
///                         var FACTORY: String,
///                         var FACTORY_SITE: String,
///                         var FAB: String,
///                         var FAB_SITE: String,
///                         var LOT_TYPE: String,
///                         var LOT_ID: String,
///                         var SBLOT_ID: String,
///                         var TEST_AREA: String,
///                         var TEST_STAGE: String,
///                         var TEST_PROGRAM: String)
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FileMetaInfo {
    /// Customer identifier
    /// Corresponds to: CUSTOMER: String
    pub customer: String,

    /// Sub customer identifier
    /// Corresponds to: SUB_CUSTOMER: String
    pub sub_customer: String,

    /// Upload type
    /// Corresponds to: UPLOAD_TYPE: String
    pub upload_type: String,

    /// File ID identifier
    /// Corresponds to: FILE_ID: java.lang.Long
    pub file_id: u64,

    /// File name
    /// Corresponds to: FILE_NAME: String
    pub file_name: String,

    /// File type
    /// Corresponds to: FILE_TYPE: String
    pub file_type: String,

    /// Device ID identifier
    /// Corresponds to: DEVICE_ID: String
    pub device_id: String,

    /// Factory identifier
    /// Corresponds to: FACTORY: String
    pub factory: String,

    /// Factory site identifier
    /// Corresponds to: FACTORY_SITE: String
    pub factory_site: String,

    /// FAB identifier
    /// Corresponds to: FAB: String
    pub fab: String,

    /// FAB site identifier
    /// Corresponds to: FAB_SITE: String
    pub fab_site: String,

    /// Lot type
    /// Corresponds to: LOT_TYPE: String
    pub lot_type: String,

    /// Lot ID identifier
    /// Corresponds to: LOT_ID: String
    pub lot_id: String,

    /// SBLOT ID identifier
    /// Corresponds to: SBLOT_ID: String
    pub sblot_id: String,

    /// Test area
    /// Corresponds to: TEST_AREA: String
    pub test_area: String,

    /// Test stage
    /// Corresponds to: TEST_STAGE: String
    pub test_stage: String,

    /// Test program
    /// Corresponds to: TEST_PROGRAM: String
    pub test_program: String,
}
