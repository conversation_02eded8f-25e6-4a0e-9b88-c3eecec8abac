use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};

use crate::dwd::model::mysql::ecid_rule::EcidRule;

// Corresponds to Scala file:
// dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/model/value/EcidParameter.scala

/// ECID field enumeration
/// Corresponds to: EcidFieldEnum in Scala constants
#[derive(Debug, <PERSON>lone, PartialEq, Hash, Eq, Serialize, Deserialize)]
pub enum EcidFieldEnum {
    LotId,
    WaferNo,
    XCoord,
    YCoord,
    Ecid,
    Uid,
}

/// Decode parameter type enumeration
/// Corresponds to: DecodeParameterTypeEnum in Scala constants
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DecodeParameterTypeEnum {
    TestTxt,
    TestItem,
    TestNum,
}

impl DecodeParameterTypeEnum {
    /// Create DecodeParameterTypeEnum from string value
    /// Corresponds to: DecodeParameterTypeEnum.of() in Scala
    pub fn of(parameter_type: &str) -> Self {
        match parameter_type.to_uppercase().as_str() {
            "TEST_TXT" => DecodeParameterTypeEnum::TestTxt,
            "TEST_ITEM" => DecodeParameterTypeEnum::TestItem,
            "TEST_NUM" => DecodeParameterTypeEnum::TestNum,
            _ => DecodeParameterTypeEnum::TestTxt, // Default
        }
    }

    /// Get string value of enum
    pub fn get_value(&self) -> &'static str {
        match self {
            DecodeParameterTypeEnum::TestTxt => "TEST_TXT",
            DecodeParameterTypeEnum::TestItem => "TEST_ITEM",
            DecodeParameterTypeEnum::TestNum => "TEST_NUM",
        }
    }
}

/// Parameters for ECID calculation from test items
///
/// Corresponds to: EcidParameter.scala
/// case class EcidParameter(var fileId: java.lang.Long,
///                          var cPartId: java.lang.Long,
///                          var ecidField: EcidFieldEnum,
///                          var parameterType: DecodeParameterTypeEnum,
///                          var xCoord: Integer,
///                          var yCoord: Integer,
///                          var hbinNum: java.lang.Long,
///                          var testParameter: String,
///                          var value: BigDecimal,
///                          var ecidRule: EcidRule)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EcidParameter {
    /// File ID identifier
    /// Corresponds to: fileId: java.lang.Long
    pub file_id: u64,

    /// C Part ID identifier
    /// Corresponds to: cPartId: java.lang.Long
    pub c_part_id: i64,

    /// ECID field type
    /// Corresponds to: ecidField: EcidFieldEnum
    pub ecid_field: EcidFieldEnum,

    /// Parameter type
    /// Corresponds to: parameterType: DecodeParameterTypeEnum
    pub parameter_type: DecodeParameterTypeEnum,

    /// X coordinate (can be null in Scala)
    /// Corresponds to: xCoord: Integer
    pub x_coord: Option<i32>,

    /// Y coordinate (can be null in Scala)
    /// Corresponds to: yCoord: Integer
    pub y_coord: Option<i32>,

    /// HBIN number (can be null in Scala)
    /// Corresponds to: hbinNum: java.lang.Long
    pub hbin_num: Option<u64>,

    /// Test parameter value
    /// Corresponds to: testParameter: String
    pub test_parameter: String,

    /// Test result value (can be null in Scala)
    /// Corresponds to: value: BigDecimal
    pub value: Option<Decimal>,

    /// ECID rule configuration
    /// Corresponds to: ecidRule: EcidRule
    pub ecid_rule: EcidRule,
}
