use std::collections::HashMap;

/// Represents DieTestInfo for broadcasting (corresponds to DieTestInfo in Scala)
#[derive(Debug, Clone)]
pub struct DieTestInfo {
    pub wafer_lot_id: Option<String>,
    pub wafer_id: Option<String>,
    pub wafer_no: Option<String>,
    pub is_first_test: Option<i32>,
    pub is_final_test: Option<i32>,
    pub is_first_test_ignore_tp: Option<i32>,
    pub is_final_test_ignore_tp: Option<i32>,
    pub max_offline_retest: Option<i32>,
    pub max_online_retest: Option<i32>,
    pub is_dup_first_test: Option<i32>,
    pub is_dup_final_test: Option<i32>,
    pub is_dup_first_test_ignore_tp: Option<i32>,
    pub is_dup_final_test_ignore_tp: Option<i32>,
    pub x_coord: Option<i32>,
    pub y_coord: Option<i32>,
    pub ecid: Option<String>,
    pub is_standard_ecid: Option<i32>,
    pub uid: Option<String>,
    pub chip_id: Option<String>,
    pub ecid_extra: Option<HashMap<String, String>>,
    pub efuse_extra: Option<HashMap<String, String>>,
    pub test_program: Option<String>,
}

impl DieTestInfo {
    pub fn new() -> Self {
        Self {
            wafer_lot_id: None,
            wafer_id: None,
            wafer_no: None,
            is_first_test: None,
            is_final_test: None,
            is_first_test_ignore_tp: None,
            is_final_test_ignore_tp: None,
            max_offline_retest: None,
            max_online_retest: None,
            is_dup_first_test: None,
            is_dup_final_test: None,
            is_dup_first_test_ignore_tp: None,
            is_dup_final_test_ignore_tp: None,
            x_coord: None,
            y_coord: None,
            ecid: None,
            is_standard_ecid: None,
            uid: None,
            chip_id: None,
            ecid_extra: None,
            efuse_extra: None,
            test_program: None,
        }
    }
}
