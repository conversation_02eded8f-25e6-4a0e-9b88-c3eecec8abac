use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// Corresponds to Scala file:
// dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/model/value/DieInfo.scala

/// Die information result from ECID processing
///
/// Corresponds to: DieInfo.scala
/// case class DieInfo(var isStandardEcid: Boolean,
///                    var ecid: String,
///                    var waferLotId: String,
///                    var waferNo: String,
///                    var xCoord: Integer,
///                    var yCoord: Integer,
///                    var uid: String,
///                    var chipId: String,
///                    var ecidExtra: Map[String, String],
///                    var efuseExtra: Map[String, String])
///
/// Note: In Scala usage, some fields are set to null, so they are Option<T> in Rust
/// Example: DieInfo(isEcid, ecid, waferLotId, waferNo, dieX, dieY, uid, null, null, null)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DieInfo {
    /// Whether this is a standard ECID
    /// Corresponds to: isStandardEcid: Boolean
    pub is_standard_ecid: bool,

    /// ECID value (can be null in Scala usage)
    /// Corresponds to: ecid: String
    pub ecid: Option<String>,

    /// Wafer lot ID (can be null in Scala usage)
    /// Corresponds to: waferLotId: String
    pub wafer_lot_id: Option<String>,

    /// Wafer number (can be null in Scala usage)
    /// Corresponds to: waferNo: String
    pub wafer_no: Option<String>,

    /// X coordinate (can be null in Scala usage)
    /// Corresponds to: xCoord: Integer
    pub x_coord: Option<i32>,

    /// Y coordinate (can be null in Scala usage)
    /// Corresponds to: yCoord: Integer
    pub y_coord: Option<i32>,

    /// UID value (can be null in Scala usage)
    /// Corresponds to: uid: String
    pub uid: Option<String>,

    /// Chip ID value (set to null in Scala usage)
    /// Corresponds to: chipId: String
    pub chip_id: Option<String>,

    /// ECID extra information (set to null in Scala usage)
    /// Corresponds to: ecidExtra: Map[String, String]
    pub ecid_extra: Option<HashMap<String, String>>,

    /// EFUSE extra information (set to null in Scala usage)
    /// Corresponds to: efuseExtra: Map[String, String]
    pub efuse_extra: Option<HashMap<String, String>>,
}

impl DieInfo {
    /// Create new DieInfo with all fields
    pub fn new(
        is_standard_ecid: bool,
        ecid: Option<String>,
        wafer_lot_id: Option<String>,
        wafer_no: Option<String>,
        x_coord: Option<i32>,
        y_coord: Option<i32>,
        uid: Option<String>,
        chip_id: Option<String>,
        ecid_extra: Option<HashMap<String, String>>,
        efuse_extra: Option<HashMap<String, String>>,
    ) -> Self {
        Self { is_standard_ecid, ecid, wafer_lot_id, wafer_no, x_coord, y_coord, uid, chip_id, ecid_extra, efuse_extra }
    }
}
