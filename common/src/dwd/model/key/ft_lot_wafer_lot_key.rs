use serde::{Deserialize, Serialize};

// Corresponds to Scala file:
// dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/model/key/FtLotWaferLotKey.scala

/// FT lot wafer lot key for lookup operations
///
/// Corresponds to: FtLotWaferLotKey.scala
/// case class FtLotWaferLotKey(var customer: String,
///                             var factory: String,
///                             var factorySite: String,
///                             var deviceId: String,
///                             var ftLot: String)
#[derive(Debug, <PERSON>lone, Hash, PartialEq, Eq, Serialize, Deserialize)]
pub struct FtLotWaferLotKey {
    /// Customer identifier
    /// Corresponds to: customer: String
    pub customer: String,

    /// Factory identifier
    /// Corresponds to: factory: String
    pub factory: String,

    /// Factory site identifier
    /// Corresponds to: factorySite: String
    pub factory_site: String,

    /// Device ID identifier
    /// Corresponds to: deviceId: String
    pub device_id: String,

    /// FT lot identifier
    /// Corresponds to: ftLot: String
    pub ft_lot: String,
}

impl FtLotWaferLotKey {
    /// Create new FtLotWaferLotKey
    pub fn new(customer: String, factory: String, factory_site: String, device_id: String, ft_lot: String) -> Self {
        Self { customer, factory, factory_site, device_id, ft_lot }
    }
}
