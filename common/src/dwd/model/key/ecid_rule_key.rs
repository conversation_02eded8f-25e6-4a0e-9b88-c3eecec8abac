use serde::{Deserialize, Serialize};

// Corresponds to Scala file:
// dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/model/key/EcidRuleKey.scala

/// ECID rule key for lookup operations
///
/// Corresponds to: EcidRuleKey.scala
/// case class EcidRuleKey(var customer: String,
///                        var deviceId: String,
///                        var testProgram: String,
///                        var testStage: String)
#[derive(Debug, <PERSON>lone, Hash, PartialEq, Eq, Serialize, Deserialize)]
pub struct EcidRuleKey {
    /// Customer identifier
    /// Corresponds to: customer: String
    pub customer: String,

    /// Device ID identifier  
    /// Corresponds to: deviceId: String
    pub device_id: String,

    /// Test program identifier
    /// Corresponds to: testProgram: String
    pub test_program: String,

    /// Test stage identifier
    /// Corresponds to: testStage: String
    pub test_stage: String,
}

impl EcidRuleKey {
    /// Create new EcidRuleKey
    pub fn new(customer: String, device_id: String, test_program: String, test_stage: String) -> Self {
        Self { customer, device_id, test_program, test_stage }
    }
}
