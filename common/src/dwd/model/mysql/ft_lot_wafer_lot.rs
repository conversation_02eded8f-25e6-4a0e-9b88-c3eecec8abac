use serde::{Deserialize, Serialize};

// Corresponds to Scala file:
// dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/model/mysql/FtLotWaferLot.scala

/// FT lot wafer lot mapping from MySQL database
///
/// Corresponds to: FtLotWaferLot.scala
/// case class FtLotWaferLot(var customer: String,
///                          var factory: String,
///                          var factory_site: String,
///                          var device_id: String,
///                          var ft_lot: String,
///                          var cp_lot: String)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FtLotWaferLot {
    /// Customer identifier
    /// Corresponds to: customer: String
    pub customer: String,

    /// Factory identifier
    /// Corresponds to: factory: String
    pub factory: String,

    /// Factory site identifier
    /// Corresponds to: factory_site: String
    pub factory_site: String,

    /// Device ID identifier
    /// Corresponds to: device_id: String
    pub device_id: String,

    /// FT lot identifier
    /// Corresponds to: ft_lot: String
    pub ft_lot: String,

    /// CP lot identifier
    /// Corresponds to: cp_lot: String
    pub cp_lot: String,
}
