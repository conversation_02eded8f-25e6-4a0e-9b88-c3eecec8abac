use serde::{Deserialize, Serialize};

// Corresponds to Scala file:
// dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/model/mysql/UidRule.scala

/// UID match type enumeration
/// Corresponds to: UidMatchType in Scala constants
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UidMatchType {
    Equals,
    EndsWith,
}

impl UidMatchType {
    /// Create UidMatchType from string value
    /// Corresponds to: UidMatchType.of() in Scala
    pub fn of(match_type: &str) -> Self {
        match match_type.to_uppercase().as_str() {
            "EQUALS" => UidMatchType::Equals,
            "ENDS_WITH" => UidMatchType::EndsWith,
            _ => UidMatchType::Equals, // Default
        }
    }
}

/// UID rule configuration from MySQL database
///
/// Corresponds to: UidRule.scala
/// case class UidRule(var customer: String,
///                    var sub_customer: String,
///                    var test_area: String,
///                    var factory: String,
///                    var factory_site: String,
///                    var parameter_uid: String,
///                    var parameter_type: String,
///                    var match_type: String,
///                    var concat_sign: String,
///                    var rule_type: String)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UidRule {
    /// Customer identifier
    /// Corresponds to: customer: String
    pub customer: String,

    /// Sub customer identifier
    /// Corresponds to: sub_customer: String
    pub sub_customer: String,

    /// Test area identifier
    /// Corresponds to: test_area: String
    pub test_area: String,

    /// Factory identifier
    /// Corresponds to: factory: String
    pub factory: String,

    /// Factory site identifier
    /// Corresponds to: factory_site: String
    pub factory_site: String,

    /// Parameter UID configuration
    /// Corresponds to: parameter_uid: String
    pub parameter_uid: String,

    /// Parameter type configuration
    /// Corresponds to: parameter_type: String
    pub parameter_type: String,

    /// Match type configuration
    /// Corresponds to: match_type: String
    pub match_type: String,

    /// Concatenation sign configuration
    /// Corresponds to: concat_sign: String
    pub concat_sign: String,

    /// Rule type configuration
    /// Corresponds to: rule_type: String
    pub rule_type: String,
}
