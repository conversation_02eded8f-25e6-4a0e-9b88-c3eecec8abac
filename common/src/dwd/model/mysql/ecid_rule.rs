use serde::{Deserialize, Serialize};

// Corresponds to Scala file:
// dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/model/mysql/EcidRule.scala

/// ECID rule configuration from MySQL database
///
/// Corresponds to: EcidRule.scala
/// case class EcidRule(var customer: String,
///                     var device_id: String,
///                     var test_program: String,
///                     var test_stage: String,
///                     var uid_flag: Int,
///                     var script: String,
///                     var decode_ecid: String,
///                     var decode_ecid_fields: String,
///                     var decode_lot_id: String,
///                     var parameter_wafer_no: String,
///                     var parameter_x_addr: String,
///                     var parameter_y_addr: String,
///                     var rule_type: String,
///                     var check_type: String,
///                     var parameter_type: String,
///                     var field_prefix: String,
///                     var rule_mode: String)
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EcidRule {
    /// Customer identifier
    /// Corresponds to: customer: String
    pub customer: String,

    /// Device ID identifier
    /// Corresponds to: device_id: String
    pub device_id: String,

    /// Test program identifier
    /// Corresponds to: test_program: String
    pub test_program: String,

    /// Test stage identifier
    /// Corresponds to: test_stage: String
    pub test_stage: String,

    /// UID flag indicator
    /// Corresponds to: uid_flag: Int
    pub uid_flag: i32,

    /// JavaScript script for ECID calculation
    /// Corresponds to: script: String
    pub script: Option<String>,

    /// Decode ECID parameters
    /// Corresponds to: decode_ecid: String
    pub decode_ecid: String,

    /// Decode ECID fields configuration
    /// Corresponds to: decode_ecid_fields: String
    pub decode_ecid_fields: String,

    /// Decode lot ID parameters
    /// Corresponds to: decode_lot_id: String
    pub decode_lot_id: String,

    /// Parameter for wafer number
    /// Corresponds to: parameter_wafer_no: String
    pub parameter_wafer_no: String,

    /// Parameter for X address
    /// Corresponds to: parameter_x_addr: String
    pub parameter_x_addr: String,

    /// Parameter for Y address
    /// Corresponds to: parameter_y_addr: String
    pub parameter_y_addr: String,

    /// Rule type configuration
    /// Corresponds to: rule_type: String
    pub rule_type: String,

    /// Check type configuration
    /// Corresponds to: check_type: String
    pub check_type: String,

    /// Parameter type configuration
    /// Corresponds to: parameter_type: String
    pub parameter_type: String,

    /// Field prefix configuration
    /// Corresponds to: field_prefix: String
    pub field_prefix: String,

    /// Rule mode configuration
    /// Corresponds to: rule_mode: String
    pub rule_mode: String,
}
