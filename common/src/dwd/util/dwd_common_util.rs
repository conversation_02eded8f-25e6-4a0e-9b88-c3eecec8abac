use crate::utils::hash::JavaHashCode;
use crate::utils::hex;
use chrono::{DateTime, Utc};

// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/util/DwdCommonUtil.scala

/// DwdCommonUtil provides common utility functions for DWD layer processing
/// This corresponds exactly to the Scala DwdCommonUtil object
pub struct DwdCommonUtil;

impl DwdCommonUtil {
    /// Calculate lot bucket for partitioning
    ///
    /// Corresponds to: DwdCommonUtil.scala:getLotBucket method  
    /// def getLotBucket(lotId: String, lotBucketNum: Int): Int = {
    ///   Math.abs(lotId.hashCode) % lotBucketNum
    /// }
    pub fn get_lot_bucket(lot_id: &str, lot_bucket_num: i32) -> i32 {
        lot_id.java_hash_code().abs() % lot_bucket_num
    }

    /// Convert null time to zero  
    ///
    /// Corresponds to: DwdCommonUtil.scala:nullTimeToZero method
    /// def nullTimeToZero(time: java.lang.Long): java.lang.Long = {
    ///   if (time == null) 0L else time  
    /// }
    pub fn null_time_to_zero(time: Option<i64>) -> i64 {
        time.unwrap_or(0)
    }

    /// Clean bin PF values
    ///
    /// This method is referenced in TestItemDetailCommonService.scala:60,110
    /// val sbinPf = DwdCommonUtil.cleanBinPf(testItemData.sbinPf, testItemData.partFlg)
    pub fn clean_bin_pf(bin_pf: &str, part_flg: Option<&str>) -> String {
        use crate::model::constant::*;
        // return !binPf.equals(P) && !binPf.equals(F) ? DwdCommonUtil.calculatePrrPF(partFlg) : binPf;
        if bin_pf != P && bin_pf != F {
            return Self::calculate_prr_pf(part_flg);
        } else {
            bin_pf.to_string()
        }
    }

    pub fn calculate_prr_pf(part_flg_str: Option<&str>) -> String {
        use crate::model::constant::*;
        if part_flg_str.is_none() || part_flg_str.unwrap().is_empty() {
            return U.to_string();
        }
        let part_flg = hex::hex_to_byte_fast(part_flg_str.unwrap());
        if part_flg[4] == '1' {
            return U.to_string();
        } else {
            if part_flg[3] == '0' {
                return P.to_string();
            } else {
                return F.to_string();
            }
        }
    }

    /// Convert value to string, handling null cases
    ///
    /// This method is referenced throughout the codebase for string conversion
    pub fn string_value(value: Option<String>) -> String {
        value.unwrap_or("".to_string())
    }

    pub fn str_value(value: Option<&str>) -> &str {
        value.unwrap_or("")
    }


    /// Calculate upload time
    ///
    /// Corresponds to: TestItemDetailHandler.scala:250
    /// DwdCommonUtil.calUploadTime(item.UPLOAD_TIME, item.CREATE_TIME)
    pub fn cal_upload_time(upload_time: Option<DateTime<Utc>>, create_time: DateTime<Utc>) -> DateTime<Utc> {
        if upload_time.is_none() {
            create_time
        } else {
            upload_time.unwrap()
        }
    }

    /// Calculate data version
    ///
    /// Corresponds to: TestItemDetailHandler.scala:251  
    /// DwdCommonUtil.calDataVersion(item.DATA_VERSION)
    pub fn cal_data_version(data_version: Option<i64>) -> i64 {
        if data_version.is_none() {
            1
        } else {
            data_version.unwrap()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_lot_bucket() {
        assert_eq!(DwdCommonUtil::get_lot_bucket("GUBO_LOT_ID_00001_3102", 6), 4);
        assert_eq!(DwdCommonUtil::get_lot_bucket("cz343002", 6), 1);
        assert_eq!(DwdCommonUtil::get_lot_bucket("NU6801QDNB-AAA1", 6), 2);
        assert_eq!(DwdCommonUtil::get_lot_bucket("AUTOTEST", 6), 3);
    }
}
