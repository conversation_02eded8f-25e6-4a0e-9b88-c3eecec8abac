use crate::parquet::Record<PERSON>atchWrapper;
use crate::utils::decimal::Decimal38_18;
use arrow::datatypes::FieldRef;
use arrow::record_batch::RecordBatch;
use serde::{Deserialize, Serialize};
use serde_arrow::schema::{SchemaLike, TracingOptions};
use std::collections::HashMap;

/// DieDetail struct for reading from parquet files
/// This matches the actual parquet schema
#[derive(Serialize, Deserialize, Debug, PartialEq, Clone)]
#[allow(non_snake_case)]
pub struct DieDetailParquet {
    pub ABRT_CNT: Option<i64>,
    pub AUX_FILE: Option<String>,
    pub BATCH_NUM: Option<i32>,
    pub BATCH_NUM_IGNORE_TP: Option<i32>,
    pub BURN_TIM: Option<i64>,
    pub CABL_ID: Option<String>,
    pub CABL_TYP: Option<String>,
    pub CENTER_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub CENTER_RETICLE_X: Option<i32>,
    pub CENTER_RETICLE_Y: Option<i32>,
    pub CENTER_X: Option<i32>,
    pub CENTER_Y: Option<i32>,
    pub CHIP_ID: Option<String>,
    pub CMOD_COD: Option<String>,
    pub CONT_ID: Option<String>,
    pub CONT_TYP: Option<String>,
    pub CREATE_DAY_KEY: Option<String>,
    pub CREATE_HOUR_KEY: Option<String>,
    pub CREATE_TIME: Option<i64>,
    pub CREATE_USER: Option<String>,
    pub CUSTOMER: Option<String>,
    pub C_PART_ID: Option<i64>,
    pub DATA_VERSION: Option<i64>,
    pub DATE_COD: Option<String>,
    pub DEVICE_ID: Option<String>,
    pub DIB_ID: Option<String>,
    pub DIB_TYP: Option<String>,
    pub DIE_CNT: Option<i64>,
    pub DIE_HEIGHT: Option<Decimal38_18>,
    pub DIE_WIDTH: Option<Decimal38_18>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub DISP_COD: Option<String>,
    pub DSGN_REV: Option<String>,
    pub DUP_RETEST: Option<i32>,
    pub DUP_RETEST_IGNORE_TP: Option<i32>,
    pub ECID: Option<String>,
    pub ECID_EXT: Option<String>,
    pub ECID_EXTRA: Option<HashMap<String, String>>,
    pub EFUSE_EXTRA: Option<HashMap<String, String>>,
    pub END_DAY_KEY: Option<String>,
    pub END_HOUR_KEY: Option<String>,
    pub END_TIME: Option<i64>,
    pub ENG_ID: Option<String>,
    pub EXEC_TYP: Option<String>,
    pub EXEC_VER: Option<String>,
    pub EXTR_ID: Option<String>,
    pub EXTR_TYP: Option<String>,
    pub FAB: Option<String>,
    pub FABWF_ID: Option<String>,
    pub FAB_SITE: Option<String>,
    pub FACIL_ID: Option<String>,
    pub FACTORY: Option<String>,
    pub FACTORY_SITE: Option<String>,
    pub FAMLY_ID: Option<String>,
    pub FILE_ID: Option<i64>,
    pub FILE_NAME: Option<String>,
    pub FILE_TYPE: Option<String>,
    pub FLOAT_ATTRIBUTE_SET: Option<HashMap<String, Decimal38_18>>,
    pub FLOOR_ID: Option<String>,
    pub FLOW_ID: Option<String>,
    pub FLOW_ID_IGNORE_TP: Option<String>,
    pub FRAME_ID: Option<String>,
    pub FUNC_CNT: Option<i64>,
    pub GOOD_CNT: Option<i64>,
    pub HBIN: Option<String>,
    pub HBIN_NAM: Option<String>,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_PF: Option<String>,
    pub ID: Option<String>,
    pub INTERRUPT: Option<i32>,
    pub INTERRUPT_IGNORE_TP: Option<i32>,
    pub IS_DELETE: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_STANDARD_ECID: Option<i32>,
    pub LASR_ID: Option<String>,
    pub LASR_TYP: Option<String>,
    pub LONG_ATTRIBUTE_SET: Option<HashMap<String, i64>>,
    pub LOT_BUCKET: Option<i32>,
    pub LOT_EXC_DESC: Option<String>,
    pub LOT_ID: Option<String>,
    pub LOT_TYPE: Option<String>,
    pub LOT_USR_DESC: Option<String>,
    pub MASK_ID: Option<String>,
    pub MAX_OFFLINE_RETEST: Option<i32>,
    pub MAX_ONLINE_RETEST: Option<i32>,
    pub MODE_COD: Option<String>,
    pub NUM_TEST: Option<i32>,
    pub OFFLINE_RETEST: Option<i32>,
    pub OFFLINE_RETEST_IGNORE_TP: Option<i32>,
    pub ONLINE_RETEST: Option<i32>,
    pub OPERATOR_NAME: Option<String>,
    pub OPER_FRQ: Option<String>,
    pub ORIGINAL_CENTER_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_X: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_Y: Option<i32>,
    pub ORIGINAL_CENTER_X: Option<i32>,
    pub ORIGINAL_CENTER_Y: Option<i32>,
    pub ORIGINAL_DIE_HEIGHT: Option<Decimal38_18>,
    pub ORIGINAL_DIE_WIDTH: Option<Decimal38_18>,
    pub ORIGINAL_POS_X: Option<String>,
    pub ORIGINAL_POS_Y: Option<String>,
    pub ORIGINAL_RETICLE_COLUMN: Option<i64>,
    pub ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_RETICLE_ROW: Option<i64>,
    pub ORIGINAL_RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_WAFER_MARGIN: Option<Decimal38_18>,
    pub ORIGINAL_WAFER_SIZE: Option<Decimal38_18>,
    pub ORIGINAL_WF_FLAT: Option<String>,
    pub ORIGINAL_WF_UNITS: Option<i64>,
    pub PART_CNT: Option<i64>,
    pub PART_FIX: Option<String>,
    pub PART_FLG: Option<String>,
    pub PART_ID: Option<String>,
    pub PART_TXT: Option<String>,
    pub PKG_TYP: Option<String>,
    pub POS_X: Option<String>,
    pub POS_Y: Option<String>,
    pub PROBECARD_LOADBOARD_ID: Option<String>,
    pub PROBECARD_LOADBOARD_TYP: Option<String>,
    pub PROBER_HANDLER_ID: Option<String>,
    pub PROBER_HANDLER_TYP: Option<String>,
    pub PROCESS: Option<String>,
    pub PROC_ID: Option<String>,
    pub PROT_COD: Option<String>,
    pub RETEST_BIN_NUM: Option<String>,
    pub RETICLE_COLUMN: Option<i64>,
    pub RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub RETICLE_ROW: Option<i64>,
    pub RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub RETICLE_T_X: Option<i32>,
    pub RETICLE_T_Y: Option<i32>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub ROM_COD: Option<String>,
    pub RTST_CNT: Option<i64>,
    pub SBIN: Option<String>,
    pub SBIN_NAM: Option<String>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_PF: Option<String>,
    pub SBLOT_ID: Option<String>,
    pub SERL_NUM: Option<String>,
    pub SETUP_ID: Option<String>,
    pub SETUP_T: Option<i64>,
    pub SITE: Option<i64>,
    pub SITE_CNT: Option<i64>,
    pub SITE_GRP: Option<i64>,
    pub SITE_ID: Option<String>,
    pub SITE_NUMS: Option<String>,
    pub SPEC_NAM: Option<String>,
    pub SPEC_VER: Option<String>,
    pub START_DAY_KEY: Option<String>,
    pub START_HOUR_KEY: Option<String>,
    pub START_TIME: Option<i64>,
    pub STAT_NUM: Option<i64>,
    pub STRING_ATTRIBUTE_SET: Option<HashMap<String, String>>,
    pub SUB_CUSTOMER: Option<String>,
    pub SUPR_NAM: Option<String>,
    pub TESTER_NAME: Option<String>,
    pub TESTER_TYPE: Option<String>,
    pub TEST_AREA: Option<String>,
    pub TEST_HEAD: Option<i64>,
    pub TEST_PROGRAM: Option<String>,
    pub TEST_PROGRAM_VERSION: Option<String>,
    pub TEST_STAGE: Option<String>,
    pub TEST_TEMPERATURE: Option<String>,
    pub TEST_TIME: Option<i64>,
    pub TEXT_DAT: Option<String>,
    pub TOUCH_DOWN_ID: Option<i32>,
    pub UID: Option<String>,
    pub UPLOAD_TIME: Option<i64>,
    pub UPLOAD_TYPE: Option<String>,
    pub USER_TXT: Option<String>,
    pub WAFER_EXC_DESC: Option<String>,
    pub WAFER_ID: Option<String>,
    pub WAFER_LOT_ID: Option<String>,
    pub WAFER_MARGIN: Option<Decimal38_18>,
    pub WAFER_NO: Option<String>,
    pub WAFER_SIZE: Option<Decimal38_18>,
    pub WAFER_USR_DESC: Option<String>,
    pub WF_FLAT: Option<String>,
    pub WF_UNITS: Option<i64>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
}

impl RecordBatchWrapper for DieDetailParquet {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<DieDetailParquet> = serde_arrow::from_record_batch(batch).unwrap();
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        let fields = Vec::<FieldRef>::from_type::<DieDetailParquet>(TracingOptions::default()).unwrap();
        let record_batch = serde_arrow::to_record_batch(&fields, &data).unwrap();
        Ok(record_batch)
    }
}
