#[derive(Debug, Clone)]
pub struct BinFailitem {
    pub customer: Option<String>,
    pub sub_customer: Option<String>,
    pub upload_type: Option<String>,
    pub factory: Option<String>,
    pub factory_site: Option<String>,
    pub fab: Option<String>,
    pub fab_site: Option<String>,
    pub test_area: Option<String>,
    pub test_stage: Option<String>,
    pub device_id: Option<String>,
    pub lot_type: Option<String>,
    pub test_program: Option<String>,
    pub test_program_version: Option<String>,
    pub test_num: Option<i64>,
    pub test_txt: Option<String>,
    pub test_item: Option<String>,
    pub testitem_type: Option<String>,
    pub units: Option<String>,
    pub origin_units: Option<String>,
    pub hbin_num: Option<i64>,
    pub hbin_num_key: Option<String>,
    pub hbin_nam: Option<String>,
    pub hbin_pf: Option<String>,
    pub hbin: Option<String>,
    pub sbin_num: Option<i64>,
    pub sbin_num_key: Option<String>,
    pub sbin_nam: Option<String>,
    pub sbin_pf: Option<String>,
    pub sbin: Option<String>,
    pub create_hour_key: Option<String>,
    pub create_day_key: Option<String>,
    pub create_time: Option<i64>,
    pub create_user: Option<String>,
    pub version: Option<i64>,
    pub upload_time: Option<i64>,
}