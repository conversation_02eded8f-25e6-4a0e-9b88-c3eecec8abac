use clickhouse::Row;
use serde::{Deserialize, Serialize};
use crate::dto::dws::item_order::ItemOrder;

#[derive(Row, Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct SubBinFailitem {
    pub CUSTOMER: Option<String>,
    pub SUB_CUSTOMER: Option<String>,
    pub UPLOAD_TYPE: Option<String>,
    pub FACTORY: Option<String>,
    pub FACTORY_SITE: Option<String>,
    pub FAB: Option<String>,
    pub FAB_SITE: Option<String>,
    pub TEST_AREA: Option<String>,
    pub TEST_STAGE: Option<String>,
    pub LOT_TYPE: Option<String>,
    pub DEVICE_ID: Option<String>,
    pub LOT_ID: Option<String>,
    pub SBLOT_ID: Option<String>,
    pub WAFER_LOT_ID: Option<String>,
    pub WAFER_ID: Option<String>,
    pub WAFER_NO: Option<String>,
    pub TEST_PROGRAM: Option<String>,
    pub TEST_TEMPERATURE: Option<String>,
    pub TEST_PROGRAM_VERSION: Option<String>,
    pub FILE_ID: Option<i64>,
    pub FILE_NAME: Option<String>,
    pub FILE_TYPE: Option<String>,
    pub TESTER_NAME: Option<String>,
    pub TESTER_TYPE: Option<String>,
    pub PROBER_HANDLER_ID: Option<String>,
    pub PROBECARD_LOADBOARD_ID: Option<String>,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: Option<String>,
    pub START_DAY_KEY: Option<String>,
    pub END_HOUR_KEY: Option<String>,
    pub END_DAY_KEY: Option<String>,
    pub FLOW_ID: Option<String>,
    pub ONLINE_RETEST: Option<i32>,
    pub ECID: Option<String>,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_NAM: Option<String>,
    pub HBIN_PF: Option<String>,
    pub HBIN: Option<String>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_NAM: Option<String>,
    pub SBIN_PF: Option<String>,
    pub SBIN: Option<String>,
    pub FIRST_FAIL_ITEM: Option<ItemOrder>,
    pub LAST_FAIL_ITEM: Option<ItemOrder>,
    pub ALL_FAIL_ITEM: Option<Vec<ItemOrder>>,
    pub ORDER_VALID_FLAG: Option<i32>,
    pub PROCESS: Option<String>,
    pub UPLOAD_TIME: Option<i64>,
}

impl SubBinFailitem {
    pub fn new() -> Self {
        Self {
            CUSTOMER: None,
            SUB_CUSTOMER: None,
            UPLOAD_TYPE: None,
            FACTORY: None,
            FACTORY_SITE: None,
            FAB: None,
            FAB_SITE: None,
            TEST_AREA: None,
            TEST_STAGE: None,
            LOT_TYPE: None,
            DEVICE_ID: None,
            LOT_ID: None,
            SBLOT_ID: None,
            WAFER_LOT_ID: None,
            WAFER_ID: None,
            WAFER_NO: None,
            TEST_PROGRAM: None,
            TEST_TEMPERATURE: None,
            TEST_PROGRAM_VERSION: None,
            FILE_ID: None,
            FILE_NAME: None,
            FILE_TYPE: None,
            TESTER_NAME: None,
            TESTER_TYPE: None,
            PROBER_HANDLER_ID: None,
            PROBECARD_LOADBOARD_ID: None,
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: None,
            START_DAY_KEY: None,
            END_HOUR_KEY: None,
            END_DAY_KEY: None,
            FLOW_ID: None,
            ONLINE_RETEST: None,
            ECID: None,
            HBIN_NUM: None,
            HBIN_NAM: None,
            HBIN_PF: None,
            HBIN: None,
            SBIN_NUM: None,
            SBIN_NAM: None,
            SBIN_PF: None,
            SBIN: None,
            FIRST_FAIL_ITEM: None,
            LAST_FAIL_ITEM: None,
            ALL_FAIL_ITEM: None,
            ORDER_VALID_FLAG: None,
            PROCESS: None,
            UPLOAD_TIME: None,
        }
    }
}