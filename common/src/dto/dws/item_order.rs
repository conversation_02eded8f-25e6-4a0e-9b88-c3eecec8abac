use clickhouse::Row;
use serde::{Deserialize, Serialize};

#[derive(Row, Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct ItemOrder {
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: Option<String>,
    pub TESTITEM_TYPE: Option<String>,
    pub TEST_ITEM: Option<String>,
    pub TEST_ORDER: Option<i64>,
    pub UNITS: Option<String>,
    pub ORIGIN_UNITS: Option<String>,
}

impl ItemOrder {
    pub fn new() -> Self {
        Self {
            TEST_NUM: None,
            TEST_TXT: None,
            TESTITEM_TYPE: None,
            TEST_ITEM: None,
            TEST_ORDER: None,
            UNITS: None,
            ORIGIN_UNITS: None,
        }
    }
}
