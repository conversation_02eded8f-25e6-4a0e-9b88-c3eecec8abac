use average::Estimate;
use average::{Mean, Variance};

// 无偏偏度计算
pub fn calculate_skewness(data: &[f64]) -> Option<f64> {
    let n = data.len() as f64;
    if n < 3.0 {
        return None; // 样本不足无法计算无偏偏度
    }

    let mean = data.iter().sum::<f64>() / n;

    let (sum2, sum3) = data.iter().fold((0.0, 0.0), |(s2, s3), &x| {
        let dev = x - mean;
        (s2 + dev * dev, s3 + dev * dev * dev)
    });

    // 计算中心矩
    let m2 = sum2 / n;
    let m3 = sum3 / n;

    if m2 <= 1e-12 {
        // 处理极小方差
        return None;
    }

    // 原始偏度计算
    let g1 = m3 / m2.powf(1.5);

    // 无偏修正系数
    let correction = (n * (n - 1.0)).sqrt() / (n - 2.0);

    Some(g1 * correction)
}

// 无偏峰度计算
pub fn calculate_kurtosis(data: &[f64]) -> Option<f64> {
    let n = data.len() as f64;
    if n < 4.0 {
        return None; // 样本不足无法计算无偏峰度
    }

    let mean = data.iter().sum::<f64>() / n;

    let (sum2, sum4) = data.iter().fold((0.0, 0.0), |(s2, s4), &x| {
        let dev = x - mean;
        let dev2 = dev * dev;
        (s2 + dev2, s4 + dev2 * dev2)
    });

    // 无偏方差计算
    let variance = sum2 / (n - 1.0);

    if variance == 0.0 {
        return None; // 方差为0无法计算峰度
    }

    // 无偏峰度计算
    let term1 = (n * (n + 1.0)) / ((n - 1.0) * (n - 2.0) * (n - 3.0));
    let term2 = sum4 / (variance.powi(2));
    let term3 = (3.0 * (n - 1.0).powi(2)) / ((n - 2.0) * (n - 3.0));

    Some(term1 * term2 - term3)
}

pub fn calculate_square_sum(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    Some(data.iter().map(|x| x * x).sum())
}

pub fn calculate_sum(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    Some(data.iter().sum())
}

pub fn calculate_mean(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut mean = Mean::new();
    data.iter().for_each(|&x| mean.add(x));
    Some(mean.mean())
}

pub fn calculate_std_dev(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut variance = Variance::new();
    data.iter().for_each(|&x| variance.add(x));
    Some(variance.estimate().sqrt())
}

pub fn calculate_quantile(data: &[f64], p: f64) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut sorted_data = data.to_vec();
    sorted_data.sort_by(|a, b| a.partial_cmp(b).unwrap());

    let n = data.len();
    let px = p / 100.0 * (n - 1) as f64;
    let i = px.floor() as usize;
    let g = px - i as f64;

    if g == 0.0 {
        Some(sorted_data[i])
    } else {
        Some((1.0 - g) * sorted_data[i] + g * sorted_data[i + 1])
    }
}

pub fn calculate_cp(std_dev: f64, low_limit: f64, high_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        f64::MAX
    } else {
        divide(high_limit - low_limit, 6.0 * std_dev)
    }
}

pub fn calculate_cpk(mean: f64, std_dev: f64, low_limit: f64, high_limit: f64) -> f64 {
    calculate_cpu(mean, std_dev, high_limit).min(calculate_cpl(mean, std_dev, low_limit))
}

pub fn calculate_cpu(mean: f64, std_dev: f64, high_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        f64::MAX
    } else {
        divide(high_limit - mean, 3.0 * std_dev)
    }
}

pub fn calculate_cpl(mean: f64, std_dev: f64, low_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        f64::MAX
    } else {
        divide(mean - low_limit, 3.0 * std_dev)
    }
}

// 辅助函数，用于保持与Scala版本一致的精度
fn equals(a: f64, b: f64) -> bool {
    (a - b).abs() < std::f64::EPSILON
}

pub fn divide(a: f64, b: f64) -> f64 {
    // 这里要跟scala版本的精度保持一致
    divide_with_scale(a, b, 6)
}

pub fn divide_with_scale(a: f64, b: f64, scale: i32) -> f64 {
    if equals(b, 0.0) {
        0.0
    } else {
        let scale_factor = 10.0_f64.powi(scale);
        (a / b * scale_factor).round() / scale_factor
    }
}

/// 计算最大值
pub fn calculate_max(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    data.iter().max_by(|a, b| a.partial_cmp(b).unwrap()).copied()
}

/// 计算最小值
pub fn calculate_min(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    data.iter().min_by(|a, b| a.partial_cmp(b).unwrap()).copied()
}

/// 计算范围（最大值 - 最小值）
pub fn calculate_range(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let max_val = calculate_max(data)?;
    let min_val = calculate_min(data)?;
    Some(max_val - min_val)
}

/// 计算第一四分位数 (Q1)
pub fn calculate_q1(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 25.0)
}

/// 计算第三四分位数 (Q3)
pub fn calculate_q3(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 75.0)
}

/// 计算四分位距 (IQR = Q3 - Q1)
pub fn calculate_iqr(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let q1 = calculate_q1(data)?;
    let q3 = calculate_q3(data)?;
    Some(q3 - q1)
}

/// 计算中位数
pub fn calculate_median(data: &[f64]) -> Option<f64> {
    calculate_quantile(data, 50.0)
}

/// 安全的小数处理函数，对应Scala中的CommonUtil.safeDecimal
/// 处理无穷大、NaN等特殊值，返回Option<f64>
pub fn safe_decimal(value: f64) -> Option<f64> {
    if value.is_finite() && !value.is_nan() {
        Some(value)
    } else {
        None
    }
}

/// 安全计算CP值，如果出现异常（如std为0）则返回None
pub fn calculate_cp_safe(std_dev: Option<f64>, lsl: Option<f64>, usl: Option<f64>) -> Option<f64> {
    if let (Some(std_dev), Some(lsl), Some(usl)) = (std_dev, lsl, usl) {
        // 当标准差为0时，CP值无意义，返回None
        if std_dev == 0.0 {
            return None;
        }
        Some(calculate_cp(std_dev, lsl, usl))
    } else {
        None
    }
}

/// 安全计算CPK值，如果出现异常（如std为0）则返回None
pub fn calculate_cpk_safe(mean: Option<f64>, std_dev: Option<f64>, lsl: Option<f64>, usl: Option<f64>) -> Option<f64> {
    if let (Some(mean), Some(std_dev), Some(lsl), Some(usl)) = (mean, std_dev, lsl, usl) {
        // 当标准差为0时，CPK值无意义，返回None
        if std_dev == 0.0 {
            return None;
        }
        Some(calculate_cpk(mean, std_dev, lsl, usl))
    } else {
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cp_calculation() {
        let values = vec![1.0, 1.1, 1.2, 1.3, 1.4, 2.0, 2.1, 5.0, 5.5, 6.0, 7.0, 8.0, 10.0, 12.0, 15.0];
        let low_limit = 1.0;
        let high_limit = 10.0;

        // 计算标准差
        let std_dev = calculate_std_dev(&values).unwrap();
        println!("Standard Deviation: {}", std_dev);

        // 计算 Cp
        let cp = calculate_cp(std_dev, low_limit, high_limit);
        println!("Cp: {}", cp);

        // 打印中间计算步骤
        let range = high_limit - low_limit;
        println!("Range (high - low): {}", range);
        let denominator = 6.0 * std_dev;
        println!("Denominator (6 * std_dev): {}", denominator);
    }

    #[test]
    fn test_cpk_calculation() {
        let values = vec![1.0, 1.1, 1.2, 1.3, 1.4, 2.0, 2.1, 5.0, 5.5, 6.0, 7.0, 8.0, 10.0, 12.0, 15.0];
        let low_limit = 1.0;
        let high_limit = 10.0;

        let std_dev = calculate_std_dev(&values).unwrap();
        let mean = calculate_mean(&values).unwrap();

        println!("Mean: {}", mean);
        println!("Standard Deviation: {}", std_dev);

        let cpu = calculate_cpu(mean, std_dev, high_limit);
        let cpl = calculate_cpl(mean, std_dev, low_limit);
        let cpk = calculate_cpk(mean, std_dev, low_limit, high_limit);

        println!("CPU: {}", cpu);
        println!("CPL: {}", cpl);
        println!("Cpk: {}", cpk);

        // 打印中间计算步骤
        println!("CPU calculation:");
        println!("  (high_limit - mean): {}", high_limit - mean);
        println!("  (3.0 * std_dev): {}", 3.0 * std_dev);

        println!("CPL calculation:");
        println!("  (mean - low_limit): {}", mean - low_limit);
        println!("  (3.0 * std_dev): {}", 3.0 * std_dev);
    }

    #[test]
    fn test_divide_with_scale() {
        assert_eq!(divide_with_scale(10.0, 3.0, 1), 3.3);
        assert_eq!(divide_with_scale(10.0, 3.0, 2), 3.33);
        assert_eq!(divide_with_scale(10.0, 3.0, 3), 3.333);
        assert_eq!(divide_with_scale(10.0, 0.0, 2), 0.0);
    }

    #[test]
    fn test_divide() {
        // 测试正常除法
        assert_eq!(divide(10.0, 2.0), 5.0);
        
        // 测试除零情况
        assert_eq!(divide(10.0, 0.0), 0.0);
        
        // 测试精度（默认6位小数）
        let result = divide(10.0, 3.0);
        assert_eq!(result, 3.333333);
        
        // 测试失败率计算场景
        assert_eq!(divide(5.0, 10.0), 0.5); // 50% 失败率
        assert_eq!(divide(0.0, 10.0), 0.0); // 0% 失败率
        assert_eq!(divide(3.0, 0.0), 0.0);  // 除零情况
    }

    #[test]
    fn test_basic_stats() {
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0];
        
        // 测试基本统计函数
        assert_eq!(calculate_max(&data), Some(10.0));
        assert_eq!(calculate_min(&data), Some(1.0));
        assert_eq!(calculate_range(&data), Some(9.0));
        assert_eq!(calculate_mean(&data).unwrap(), 5.5);
        
        // 测试四分位数
        assert_eq!(calculate_q1(&data), Some(3.25));
        assert_eq!(calculate_q3(&data), Some(7.75));
        assert_eq!(calculate_iqr(&data), Some(4.5));
        assert_eq!(calculate_median(&data), Some(5.5));
        
        // 测试空数据
        let empty_data: Vec<f64> = vec![];
        assert_eq!(calculate_max(&empty_data), None);
        assert_eq!(calculate_min(&empty_data), None);
        assert_eq!(calculate_range(&empty_data), None);
        assert_eq!(calculate_mean(&empty_data), None);
        assert_eq!(calculate_q1(&empty_data), None);
        assert_eq!(calculate_q3(&empty_data), None);
        assert_eq!(calculate_iqr(&empty_data), None);
        assert_eq!(calculate_median(&empty_data), None);
    }

    #[test]
    fn test_safe_decimal() {
        // 测试正常值
        assert_eq!(safe_decimal(1.5), Some(1.5));
        assert_eq!(safe_decimal(0.0), Some(0.0));
        assert_eq!(safe_decimal(-1.5), Some(-1.5));
        
        // 测试特殊值
        assert_eq!(safe_decimal(f64::INFINITY), None);
        assert_eq!(safe_decimal(f64::NEG_INFINITY), None);
        assert_eq!(safe_decimal(f64::NAN), None);
    }

    #[test]
    fn test_calculate_cp_safe() {
        // 测试正常情况
        let result = calculate_cp_safe(Some(0.5), Some(1.0), Some(10.0));
        assert!(result.is_some());
        assert!(result.unwrap() > 0.0);
        
        // 测试缺少参数的情况
        assert_eq!(calculate_cp_safe(None, Some(1.0), Some(10.0)), None);
        assert_eq!(calculate_cp_safe(Some(0.5), None, Some(10.0)), None);
        assert_eq!(calculate_cp_safe(Some(0.5), Some(1.0), None), None);
        
        // 测试标准差为0的情况
        let result_zero_std = calculate_cp_safe(Some(0.0), Some(1.0), Some(10.0));
        assert!(result_zero_std.is_none()); // 因为会产生无穷大值
    }

    #[test]
    fn test_calculate_cpk_safe() {
        // 测试正常情况
        let result = calculate_cpk_safe(Some(5.0), Some(0.5), Some(1.0), Some(10.0));
        assert!(result.is_some());
        assert!(result.unwrap() > 0.0);
        
        // 测试缺少参数的情况
        assert_eq!(calculate_cpk_safe(None, Some(0.5), Some(1.0), Some(10.0)), None);
        assert_eq!(calculate_cpk_safe(Some(5.0), None, Some(1.0), Some(10.0)), None);
        assert_eq!(calculate_cpk_safe(Some(5.0), Some(0.5), None, Some(10.0)), None);
        assert_eq!(calculate_cpk_safe(Some(5.0), Some(0.5), Some(1.0), None), None);
        
        // 测试标准差为0的情况
        let result_zero_std = calculate_cpk_safe(Some(5.0), Some(0.0), Some(1.0), Some(10.0));
        assert!(result_zero_std.is_none()); // 因为会产生无穷大值
    }
}
