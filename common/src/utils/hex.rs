// Copyright (C), 2021, guwave
// HexUtil (Rust version)
// Author: <PERSON> (ported by AI)
// 2021-11-22 10:41:29

/// 反转字符数组
fn reverse(arr: &[char]) -> Vec<char> {
    let mut result = arr.to_vec();
    result.reverse();
    result
}

/// 十进制转十六进制后每两位转 ASCII 字符
pub fn transform_ascii(data: u32) -> String {
    let hex_str = format!("{:x}", data);
    let mut sb = String::new();
    let chars: Vec<char> = hex_str.chars().collect();
    let mut i = 0;
    while i + 1 < chars.len() {
        let pair = format!("{}{}", chars[i], chars[i + 1]);
        if let Ok(val) = u8::from_str_radix(&pair, 16) {
            sb.push(val as char);
        }
        i += 2;
    }
    sb
}

/// Hex 字符串转 byte（返回反转后的 8 位二进制字符数组）
pub fn hex_to_byte(in_hex: &str) -> Vec<char> {
    if let Ok(b) = u8::from_str_radix(in_hex, 16) {
        let bin_str = format!("{:08b}", b);
        let chars: Vec<char> = bin_str.chars().collect();
        reverse(&chars)
    } else {
        vec![]
    }
}

pub fn hex_to_byte_fast(in_hex: &str) -> Vec<char> {
    let b = u8::from_str_radix(in_hex, 16).unwrap();
    let s = format!(
        "{}{}{}{}{}{}{}{}",
        b >> 0 & 0x1,
        b >> 1 & 0x1,
        b >> 2 & 0x1,
        b >> 3 & 0x1,
        b >> 4 & 0x1,
        b >> 5 & 0x1,
        b >> 6 & 0x1,
        b >> 7 & 0x1
    );
    s.chars().collect()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_reverse() {
        let arr = vec!['a', 'b', 'c'];
        assert_eq!(reverse(&arr), vec!['c', 'b', 'a']);
    }

    #[test]
    fn test_transform_ascii() {
        assert_eq!(transform_ascii(13112), "38");
        assert_eq!(transform_ascii(89), "Y");
    }

    #[test]
    fn test_hex_to_byte() {
        assert_eq!(hex_to_byte("2"), vec!['0', '1', '0', '0', '0', '0', '0', '0']);
        assert_eq!(hex_to_byte("ff"), vec!['1', '1', '1', '1', '1', '1', '1', '1']);
    }

    #[test]
    fn test_hex_to_byte_fast() {
        assert_eq!(hex_to_byte_fast("2"), vec!['0', '1', '0', '0', '0', '0', '0', '0']);
        assert_eq!(hex_to_byte_fast("ff"), vec!['1', '1', '1', '1', '1', '1', '1', '1']);
    }
}
