//! CkUtil工具类
//!
//! 对应Scala中的ScalaCkUtil，提供ClickHouse相关的工具方法

/// CkUtil工具类
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.util.ScalaCkUtil
pub struct CkUtil;

impl CkUtil {
    /// 获取DWS替换MergeTree分区表达式（包含deviceId）
    /// 对应Scala中的getDwsReplaceMergeTreePartition(customer, testArea, factory, subCustomer, deviceId)方法
    /// 
    /// # 参数
    /// * `customer` - 客户
    /// * `test_area` - 测试区域
    /// * `factory` - 工厂
    /// * `sub_customer` - 子客户
    /// * `device_id` - 设备ID
    /// 
    /// # 返回
    /// 分区表达式字符串，格式：('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{SUB_CUSTOMER}','{DEVICE_ID}')
    pub fn get_dws_replace_merge_tree_partition_with_device(
        customer: &str,
        test_area: &str,
        factory: &str,
        sub_customer: &str,
        device_id: &str,
    ) -> String {
        // 对应Scala中的DWS_REPLACE_MERGE_TREE_PARTITION_WITH_DEVICE_TEMPLATE
        // "('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{SUB_CUSTOMER}','{DEVICE_ID}')"
        format!(
            "('{}','AUTO','{}','{}','{}','{}')",
            customer, test_area, factory, sub_customer, device_id
        )
    }
    
    /// 获取DWS替换MergeTree分区表达式（不包含deviceId）
    /// 对应Scala中的getDwsReplaceMergeTreePartition(customer, testArea, factory, subCustomer)方法
    /// 
    /// # 参数
    /// * `customer` - 客户
    /// * `test_area` - 测试区域
    /// * `factory` - 工厂
    /// * `sub_customer` - 子客户
    /// 
    /// # 返回
    /// 分区表达式字符串，格式：('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{SUB_CUSTOMER}')
    pub fn get_dws_replace_merge_tree_partition(
        customer: &str,
        test_area: &str,
        factory: &str,
        sub_customer: &str,
    ) -> String {
        // 对应Scala中的DWS_REPLACE_MERGE_TREE_PARTITION_TEMPLATE
        // "('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{SUB_CUSTOMER}')"
        format!(
            "('{}','AUTO','{}','{}','{}')",
            customer, test_area, factory, sub_customer
        )
    }
    
    /// 获取测试程序分区表达式
    /// 对应Scala中的getTestProgramPartition方法
    /// 
    /// # 参数
    /// * `customer` - 客户
    /// * `test_area` - 测试区域
    /// * `factory` - 工厂
    /// 
    /// # 返回
    /// 分区表达式字符串，格式：('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}')
    pub fn get_test_program_partition(
        customer: &str,
        test_area: &str,
        factory: &str,
    ) -> String {
        // 对应Scala中的DIM_TEST_PROGRAM_PARTITION_TEMPLATE
        format!(
            "('{}','AUTO','{}','{}')",
            customer, test_area, factory
        )
    }
    
    /// 获取批次晶圆分区表达式
    /// 对应Scala中的getLotWaferPartition方法
    /// 
    /// # 参数
    /// * `customer` - 客户
    /// * `test_area` - 测试区域
    /// * `factory` - 工厂
    /// * `sub_customer` - 子客户
    /// 
    /// # 返回
    /// 分区表达式字符串，格式：('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{SUB_CUSTOMER}')
    pub fn get_lot_wafer_partition(
        customer: &str,
        test_area: &str,
        factory: &str,
        sub_customer: &str,
    ) -> String {
        // 对应Scala中的DIM_LOT_WAFER_PARTITION_TEMPLATE
        format!(
            "('{}','AUTO','{}','{}','{}')",
            customer, test_area, factory, sub_customer
        )
    }
    
    /// 获取测试项分区表达式
    /// 对应Scala中的getTestItemPartition方法
    /// 
    /// # 参数
    /// * `customer` - 客户
    /// * `test_area` - 测试区域
    /// * `factory` - 工厂
    /// * `sub_customer` - 子客户
    /// * `device_id` - 设备ID
    /// 
    /// # 返回
    /// 分区表达式字符串，格式：('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{SUB_CUSTOMER}','{DEVICE_ID}')
    pub fn get_test_item_partition(
        customer: &str,
        test_area: &str,
        factory: &str,
        sub_customer: &str,
        device_id: &str,
    ) -> String {
        // 对应Scala中的DIM_TEST_ITEM_PARTITION_TEMPLATE
        format!(
            "('{}','AUTO','{}','{}','{}','{}')",
            customer, test_area, factory, sub_customer, device_id
        )
    }
    
    /// 获取优化表SQL
    /// 对应Scala中的getOptimizeTableSql方法
    /// 
    /// # 参数
    /// * `insert_sql` - 插入SQL语句
    /// * `partition` - 分区表达式
    /// 
    /// # 返回
    /// 优化表的SQL语句
    pub fn get_optimize_table_sql(insert_sql: &str, partition: &str) -> String {
        // 从插入SQL中提取表名
        // 格式：INSERT INTO db.table ...
        let parts: Vec<&str> = insert_sql.split_whitespace().collect();
        let table_name = if parts.len() >= 3 {
            parts[2] // INSERT INTO table_name
        } else {
            "unknown_table"
        };
        
        // 对应Scala中的OPTIMIZE_TABLE_TEMPLATE
        format!("OPTIMIZE TABLE {} PARTITION {}", table_name, partition)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_dws_replace_merge_tree_partition_with_device() {
        let result = CkUtil::get_dws_replace_merge_tree_partition_with_device(
            "CUSTOMER1",
            "CP",
            "FACTORY1",
            "SUB1",
            "DEVICE1"
        );
        assert_eq!(result, "('CUSTOMER1','AUTO','CP','FACTORY1','SUB1','DEVICE1')");
    }

    #[test]
    fn test_get_dws_replace_merge_tree_partition() {
        let result = CkUtil::get_dws_replace_merge_tree_partition(
            "CUSTOMER1",
            "CP",
            "FACTORY1",
            "SUB1"
        );
        assert_eq!(result, "('CUSTOMER1','AUTO','CP','FACTORY1','SUB1')");
    }

    #[test]
    fn test_get_test_program_partition() {
        let result = CkUtil::get_test_program_partition(
            "CUSTOMER1",
            "CP",
            "FACTORY1"
        );
        assert_eq!(result, "('CUSTOMER1','AUTO','CP','FACTORY1')");
    }

    #[test]
    fn test_get_lot_wafer_partition() {
        let result = CkUtil::get_lot_wafer_partition(
            "CUSTOMER1",
            "CP",
            "FACTORY1",
            "SUB1"
        );
        assert_eq!(result, "('CUSTOMER1','AUTO','CP','FACTORY1','SUB1')");
    }

    #[test]
    fn test_get_test_item_partition() {
        let result = CkUtil::get_test_item_partition(
            "CUSTOMER1",
            "CP",
            "FACTORY1",
            "SUB1",
            "DEVICE1"
        );
        assert_eq!(result, "('CUSTOMER1','AUTO','CP','FACTORY1','SUB1','DEVICE1')");
    }

    #[test]
    fn test_get_optimize_table_sql() {
        let insert_sql = "INSERT INTO test_db.test_table (col1, col2) VALUES (?, ?)";
        let partition = "('CUSTOMER1','AUTO','CP','FACTORY1')";
        let result = CkUtil::get_optimize_table_sql(insert_sql, partition);
        assert_eq!(result, "OPTIMIZE TABLE test_db.test_table PARTITION ('CUSTOMER1','AUTO','CP','FACTORY1')");
    }
}