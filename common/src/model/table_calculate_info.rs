use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime, UNIX_EPOCH};

/// TableCalculateInfo tracks timing and resource usage for table calculations
/// Corresponds to: TableCalculateInfo value class in Scala implementation
/// Used for monitoring and performance tracking of data warehouse operations
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct TableCalculateInfo {
    /// Start time of the calculation step - Corresponds to Scala TableCalculateInfo.startTime
    pub start_time: Option<i64>,

    /// End time of the calculation step - Corresponds to Scala TableCalculateInfo.endTime
    pub end_time: Option<i64>,

    /// Step name or identifier - Corresponds to Scala TableCalculateInfo.step
    pub step: String,

    /// Duration in milliseconds - Calculated field
    pub duration_ms: Option<i64>,

    /// Memory usage in bytes - Corresponds to Scala TableCalculateInfo resource tracking
    pub memory_usage_bytes: Option<i64>,

    /// CPU usage percentage - Corresponds to Scala TableCalculateInfo resource tracking
    pub cpu_usage_percent: Option<f64>,

    /// Number of records processed - Corresponds to Scala TableCalculateInfo metrics
    pub records_processed: Option<i64>,

    /// Data size in bytes - Corresponds to Scala TableCalculateInfo metrics
    pub data_size_bytes: Option<i64>,

    /// Error message if calculation failed - Corresponds to Scala TableCalculateInfo error tracking
    pub error_message: Option<String>,

    /// Success flag - Corresponds to Scala TableCalculateInfo status tracking
    pub success: bool,
}

impl TableCalculateInfo {
    /// Create a new TableCalculateInfo instance
    /// Corresponds to: TableCalculateInfo constructor in Scala
    pub fn new(step: String) -> Self {
        Self {
            start_time: None,
            end_time: None,
            step,
            duration_ms: None,
            memory_usage_bytes: None,
            cpu_usage_percent: None,
            records_processed: None,
            data_size_bytes: None,
            error_message: None,
            success: false,
        }
    }

    /// Start timing for this calculation step
    /// Corresponds to: Starting calculation timing in Scala (line 47: CALCULATE_START)
    pub fn start(&mut self) {
        self.start_time = Some(Self::current_timestamp_millis());
        self.success = false;
        self.error_message = None;
    }

    /// End timing for this calculation step
    /// Corresponds to: Ending calculation timing in Scala (line 63: CALCULATE_END)
    pub fn end(&mut self) {
        self.end_time = Some(Self::current_timestamp_millis());
        if let (Some(start), Some(end)) = (self.start_time, self.end_time) {
            self.duration_ms = Some(end - start);
        }
        self.success = true;
    }

    /// End timing with error
    /// Corresponds to: Error handling in Scala calculation tracking
    pub fn end_with_error(&mut self, error: String) {
        self.end_time = Some(Self::current_timestamp_millis());
        if let (Some(start), Some(end)) = (self.start_time, self.end_time) {
            self.duration_ms = Some(end - start);
        }
        self.error_message = Some(error);
        self.success = false;
    }

    /// Set resource usage metrics
    /// Corresponds to: Resource tracking in Scala TableCalculateInfo
    pub fn set_resource_usage(&mut self, memory_bytes: Option<i64>, cpu_percent: Option<f64>) {
        self.memory_usage_bytes = memory_bytes;
        self.cpu_usage_percent = cpu_percent;
    }

    /// Set data processing metrics
    /// Corresponds to: Data metrics tracking in Scala TableCalculateInfo
    pub fn set_data_metrics(&mut self, records: Option<i64>, size_bytes: Option<i64>) {
        self.records_processed = records;
        self.data_size_bytes = size_bytes;
    }

    /// Get duration in milliseconds
    /// Corresponds to: Duration calculation in Scala TableCalculateInfo
    pub fn get_duration_ms(&self) -> Option<i64> {
        self.duration_ms
    }

    /// Get duration in seconds
    /// Corresponds to: Duration conversion in Scala TableCalculateInfo
    pub fn get_duration_seconds(&self) -> Option<f64> {
        self.duration_ms.map(|ms| ms as f64 / 1000.0)
    }

    /// Check if calculation was successful
    /// Corresponds to: Success status checking in Scala TableCalculateInfo
    pub fn is_successful(&self) -> bool {
        self.success && self.error_message.is_none()
    }

    /// Get current timestamp in milliseconds
    /// Corresponds to: System.currentTimeMillis() in Scala
    fn current_timestamp_millis() -> i64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or(Duration::from_secs(0))
            .as_millis() as i64
    }

    /// Convert to DateTime for database storage
    /// Corresponds to: Timestamp conversion for database operations in Scala
    pub fn start_time_as_datetime(&self) -> Option<DateTime<Utc>> {
        self.start_time
            .map(|ts| DateTime::from_timestamp_millis(ts).unwrap_or_else(Utc::now))
    }

    /// Convert to DateTime for database storage
    /// Corresponds to: Timestamp conversion for database operations in Scala
    pub fn end_time_as_datetime(&self) -> Option<DateTime<Utc>> {
        self.end_time
            .map(|ts| DateTime::from_timestamp_millis(ts).unwrap_or_else(Utc::now))
    }
}

impl Default for TableCalculateInfo {
    fn default() -> Self {
        Self::new(String::new())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    use std::time::Duration;

    #[test]
    fn test_table_calculate_info_creation() {
        let info = TableCalculateInfo::new("TEST_STEP".to_string());
        assert_eq!(info.step, "TEST_STEP");
        assert!(info.start_time.is_none());
        assert!(info.end_time.is_none());
        assert!(!info.success);
    }

    #[test]
    fn test_timing_functionality() {
        let mut info = TableCalculateInfo::new("TIMING_TEST".to_string());

        info.start();
        assert!(info.start_time.is_some());
        assert!(!info.success);

        // Small delay to ensure different timestamps
        thread::sleep(Duration::from_millis(10));

        info.end();
        assert!(info.end_time.is_some());
        assert!(info.success);
        assert!(info.duration_ms.is_some());
        assert!(info.duration_ms.unwrap() >= 10);
    }

    #[test]
    fn test_error_handling() {
        let mut info = TableCalculateInfo::new("ERROR_TEST".to_string());

        info.start();
        info.end_with_error("Test error message".to_string());

        assert!(!info.success);
        assert!(info.error_message.is_some());
        assert_eq!(info.error_message.as_ref().unwrap(), "Test error message");
        assert!(!info.is_successful());
    }

    #[test]
    fn test_resource_metrics() {
        let mut info = TableCalculateInfo::new("RESOURCE_TEST".to_string());

        info.set_resource_usage(Some(1024 * 1024), Some(75.5));
        info.set_data_metrics(Some(10000), Some(2048 * 1024));

        assert_eq!(info.memory_usage_bytes, Some(1024 * 1024));
        assert_eq!(info.cpu_usage_percent, Some(75.5));
        assert_eq!(info.records_processed, Some(10000));
        assert_eq!(info.data_size_bytes, Some(2048 * 1024));
    }

    #[test]
    fn test_duration_conversion() {
        let mut info = TableCalculateInfo::new("DURATION_TEST".to_string());
        info.duration_ms = Some(5000);

        assert_eq!(info.get_duration_seconds(), Some(5.0));
    }

    #[test]
    fn test_datetime_conversion() {
        let mut info = TableCalculateInfo::new("DATETIME_TEST".to_string());
        info.start();

        let start_dt = info.start_time_as_datetime();
        assert!(start_dt.is_some());

        info.end();
        let end_dt = info.end_time_as_datetime();
        assert!(end_dt.is_some());

        // End time should be after start time
        assert!(end_dt.unwrap() >= start_dt.unwrap());
    }
}
