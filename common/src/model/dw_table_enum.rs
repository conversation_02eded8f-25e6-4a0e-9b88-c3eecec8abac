use serde::{Deserialize, Serialize};
use std::fmt;

/// DwTableEnum represents the different data warehouse table types
/// Corresponds to: DwTableEnum in Scala implementation
/// Used for tracking calculation metrics and table operations
#[derive(Debug, <PERSON><PERSON>, <PERSON>h, PartialEq, Eq, Serialize, Deserialize)]
pub enum DwTableEnum {
    /// DWD Test Item Detail table - Corresponds to Scala DwTableEnum.DWD_TEST_ITEM_DETAIL
    DwdTestItemDetail,

    /// DWD Die Detail table - Corresponds to Scala DwTableEnum.DWD_DIE_DETAIL
    DwdDieDetail,

    /// DWS Test Item Summary table - Corresponds to Scala DwTableEnum.DWS_TEST_ITEM_SUMMARY
    DwsTestItemSummary,

    /// DWS Die Summary table - Corresponds to Scala DwTableEnum.DWS_DIE_SUMMARY
    DwsDieSummary,

    /// ADS Test Item Analysis table - Corresponds to Scala DwTableEnum.ADS_TEST_ITEM_ANALYSIS
    AdsTestItemAnalysis,

    /// ADS Die Analysis table - Corresponds to Scala DwTableEnum.ADS_DIE_ANALYSIS
    AdsDieAnalysis,

    /// DIM Customer table - Corresponds to Scala DwTableEnum.DIM_CUSTOMER
    DimCustomer,

    /// DIM Device table - Corresponds to Scala DwTableEnum.DIM_DEVICE
    DimDevice,

    /// DIM Test Program table - Corresponds to Scala DwTableEnum.DIM_TEST_PROGRAM
    DimTestProgram,
}

impl DwTableEnum {
    /// Get the table name as string
    /// Corresponds to: DwTableEnum.getTableName() in Scala
    pub fn table_name(&self) -> &'static str {
        match self {
            DwTableEnum::DwdTestItemDetail => "dwd_test_item_detail",
            DwTableEnum::DwdDieDetail => "dwd_die_detail",
            DwTableEnum::DwsTestItemSummary => "dws_test_item_summary",
            DwTableEnum::DwsDieSummary => "dws_die_summary",
            DwTableEnum::AdsTestItemAnalysis => "ads_test_item_analysis",
            DwTableEnum::AdsDieAnalysis => "ads_die_analysis",
            DwTableEnum::DimCustomer => "dim_customer",
            DwTableEnum::DimDevice => "dim_device",
            DwTableEnum::DimTestProgram => "dim_test_program",
        }
    }

    /// Get the data warehouse layer
    /// Corresponds to: DwTableEnum.getLayer() in Scala
    pub fn layer(&self) -> DwLayer {
        match self {
            DwTableEnum::DwdTestItemDetail | DwTableEnum::DwdDieDetail => DwLayer::DWD,
            DwTableEnum::DwsTestItemSummary | DwTableEnum::DwsDieSummary => DwLayer::DWS,
            DwTableEnum::AdsTestItemAnalysis | DwTableEnum::AdsDieAnalysis => DwLayer::ADS,
            DwTableEnum::DimCustomer | DwTableEnum::DimDevice | DwTableEnum::DimTestProgram => DwLayer::DIM,
        }
    }

    /// Check if this is a test item related table
    /// Corresponds to: Business logic in Scala for test item tables
    pub fn is_test_item_table(&self) -> bool {
        matches!(
            self,
            DwTableEnum::DwdTestItemDetail | DwTableEnum::DwsTestItemSummary | DwTableEnum::AdsTestItemAnalysis
        )
    }

    /// Check if this is a die related table
    /// Corresponds to: Business logic in Scala for die tables
    pub fn is_die_table(&self) -> bool {
        matches!(self, DwTableEnum::DwdDieDetail | DwTableEnum::DwsDieSummary | DwTableEnum::AdsDieAnalysis)
    }
}

impl fmt::Display for DwTableEnum {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.table_name())
    }
}

/// DwLayer represents the different data warehouse layers
/// Corresponds to: DwLayer enum in Scala implementation
#[derive(Debug, Clone, Hash, PartialEq, Eq, Serialize, Deserialize)]
pub enum DwLayer {
    /// Operational Data Store - Corresponds to Scala DwLayer.ODS
    ODS,

    /// Data Warehouse Detail - Corresponds to Scala DwLayer.DWD
    DWD,

    /// Data Warehouse Summary - Corresponds to Scala DwLayer.DWS
    DWS,

    /// Application Data Service - Corresponds to Scala DwLayer.ADS
    ADS,

    /// Dimension - Corresponds to Scala DwLayer.DIM
    DIM,
}

impl DwLayer {
    /// Get the layer name as string
    /// Corresponds to: DwLayer.getLayerName() in Scala
    pub fn layer_name(&self) -> &'static str {
        match self {
            DwLayer::ODS => "ods",
            DwLayer::DWD => "dwd",
            DwLayer::DWS => "dws",
            DwLayer::ADS => "ads",
            DwLayer::DIM => "dim",
        }
    }

    /// Get the processing order (lower numbers processed first)
    /// Corresponds to: Processing order logic in Scala
    pub fn processing_order(&self) -> u8 {
        match self {
            DwLayer::ODS => 1,
            DwLayer::DWD => 2,
            DwLayer::DIM => 3,
            DwLayer::DWS => 4,
            DwLayer::ADS => 5,
        }
    }
}

impl fmt::Display for DwLayer {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.layer_name())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_dw_table_enum_table_name() {
        assert_eq!(DwTableEnum::DwdTestItemDetail.table_name(), "dwd_test_item_detail");
        assert_eq!(DwTableEnum::DwdDieDetail.table_name(), "dwd_die_detail");
        assert_eq!(DwTableEnum::DwsTestItemSummary.table_name(), "dws_test_item_summary");
    }

    #[test]
    fn test_dw_table_enum_layer() {
        assert_eq!(DwTableEnum::DwdTestItemDetail.layer(), DwLayer::DWD);
        assert_eq!(DwTableEnum::DwsTestItemSummary.layer(), DwLayer::DWS);
        assert_eq!(DwTableEnum::AdsTestItemAnalysis.layer(), DwLayer::ADS);
        assert_eq!(DwTableEnum::DimCustomer.layer(), DwLayer::DIM);
    }

    #[test]
    fn test_dw_table_enum_type_checks() {
        assert!(DwTableEnum::DwdTestItemDetail.is_test_item_table());
        assert!(!DwTableEnum::DwdTestItemDetail.is_die_table());

        assert!(DwTableEnum::DwdDieDetail.is_die_table());
        assert!(!DwTableEnum::DwdDieDetail.is_test_item_table());

        assert!(!DwTableEnum::DimCustomer.is_test_item_table());
        assert!(!DwTableEnum::DimCustomer.is_die_table());
    }

    #[test]
    fn test_dw_table_enum_hash_map_usage() {
        let mut map = HashMap::new();
        map.insert(DwTableEnum::DwdTestItemDetail, "test_value");

        assert!(map.contains_key(&DwTableEnum::DwdTestItemDetail));
        assert_eq!(map.get(&DwTableEnum::DwdTestItemDetail), Some(&"test_value"));
    }

    #[test]
    fn test_dw_layer_processing_order() {
        assert!(DwLayer::ODS.processing_order() < DwLayer::DWD.processing_order());
        assert!(DwLayer::DWD.processing_order() < DwLayer::DWS.processing_order());
        assert!(DwLayer::DWS.processing_order() < DwLayer::ADS.processing_order());
    }

    #[test]
    fn test_dw_layer_display() {
        assert_eq!(format!("{}", DwLayer::DWD), "dwd");
        assert_eq!(format!("{}", DwLayer::DWS), "dws");
    }

    #[test]
    fn test_dw_table_enum_display() {
        assert_eq!(format!("{}", DwTableEnum::DwdTestItemDetail), "dwd_test_item_detail");
    }
}
