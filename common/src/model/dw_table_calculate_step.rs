use serde::{Deserialize, Serialize};
use std::fmt;

/// DwTableCalculateStep represents the different steps in table calculation process
/// Corresponds to: DwTableCalculateStep constants in Scala implementation
/// Used for tracking calculation progress and timing metrics
#[derive(Debug, <PERSON><PERSON>, <PERSON>h, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum DwTableCalculateStep {
    /// Calculation start step - Corresponds to Scala DwTableCalculateStep.CALCULATE_START
    CalculateStart,

    /// Calculation end step - Corresponds to Scala DwTableCalculateStep.CALCULATE_END
    CalculateEnd,

    /// ClickHouse sink start step - Corresponds to Scala DwTableCalculateStep.SINK_CK_START
    SinkCkStart,

    /// ClickHouse sink end step - Corresponds to Scala DwTableCalculateStep.SINK_CK_END
    SinkCkEnd,

    /// HDFS sink start step - Corresponds to Scala DwTableCalculateStep.SINK_HDFS_START
    SinkHdfsStart,

    /// HDFS sink end step - Corresponds to Scala DwTableCalculateStep.SINK_HDFS_END
    SinkHdfsEnd,

    /// Data validation start step - Corresponds to Scala DwTableCalculateStep.VALIDATE_START
    ValidateStart,

    /// Data validation end step - Corresponds to Scala DwTableCalculateStep.VALIDATE_END
    ValidateEnd,

    /// Data transformation start step - Corresponds to Scala DwTableCalculateStep.TRANSFORM_START
    TransformStart,

    /// Data transformation end step - Corresponds to Scala DwTableCalculateStep.TRANSFORM_END
    TransformEnd,
}

impl DwTableCalculateStep {
    /// Get the step name as string
    /// Corresponds to: Step name constants in Scala
    pub fn step_name(&self) -> &'static str {
        match self {
            DwTableCalculateStep::CalculateStart => "CALCULATE_START",
            DwTableCalculateStep::CalculateEnd => "CALCULATE_END",
            DwTableCalculateStep::SinkCkStart => "SINK_CK_START",
            DwTableCalculateStep::SinkCkEnd => "SINK_CK_END",
            DwTableCalculateStep::SinkHdfsStart => "SINK_HDFS_START",
            DwTableCalculateStep::SinkHdfsEnd => "SINK_HDFS_END",
            DwTableCalculateStep::ValidateStart => "VALIDATE_START",
            DwTableCalculateStep::ValidateEnd => "VALIDATE_END",
            DwTableCalculateStep::TransformStart => "TRANSFORM_START",
            DwTableCalculateStep::TransformEnd => "TRANSFORM_END",
        }
    }

    /// Check if this is a start step
    /// Corresponds to: Step type checking logic in Scala
    pub fn is_start_step(&self) -> bool {
        matches!(
            self,
            DwTableCalculateStep::CalculateStart
                | DwTableCalculateStep::SinkCkStart
                | DwTableCalculateStep::SinkHdfsStart
                | DwTableCalculateStep::ValidateStart
                | DwTableCalculateStep::TransformStart
        )
    }

    /// Check if this is an end step
    /// Corresponds to: Step type checking logic in Scala
    pub fn is_end_step(&self) -> bool {
        matches!(
            self,
            DwTableCalculateStep::CalculateEnd
                | DwTableCalculateStep::SinkCkEnd
                | DwTableCalculateStep::SinkHdfsEnd
                | DwTableCalculateStep::ValidateEnd
                | DwTableCalculateStep::TransformEnd
        )
    }

    /// Get the corresponding end step for a start step
    /// Corresponds to: Step pairing logic in Scala
    pub fn get_end_step(&self) -> Option<DwTableCalculateStep> {
        match self {
            DwTableCalculateStep::CalculateStart => Some(DwTableCalculateStep::CalculateEnd),
            DwTableCalculateStep::SinkCkStart => Some(DwTableCalculateStep::SinkCkEnd),
            DwTableCalculateStep::SinkHdfsStart => Some(DwTableCalculateStep::SinkHdfsEnd),
            DwTableCalculateStep::ValidateStart => Some(DwTableCalculateStep::ValidateEnd),
            DwTableCalculateStep::TransformStart => Some(DwTableCalculateStep::TransformEnd),
            _ => None,
        }
    }

    /// Get the corresponding start step for an end step
    /// Corresponds to: Step pairing logic in Scala
    pub fn get_start_step(&self) -> Option<DwTableCalculateStep> {
        match self {
            DwTableCalculateStep::CalculateEnd => Some(DwTableCalculateStep::CalculateStart),
            DwTableCalculateStep::SinkCkEnd => Some(DwTableCalculateStep::SinkCkStart),
            DwTableCalculateStep::SinkHdfsEnd => Some(DwTableCalculateStep::SinkHdfsStart),
            DwTableCalculateStep::ValidateEnd => Some(DwTableCalculateStep::ValidateStart),
            DwTableCalculateStep::TransformEnd => Some(DwTableCalculateStep::TransformStart),
            _ => None,
        }
    }

    /// Get the step category (calculate, sink, validate, transform)
    /// Corresponds to: Step categorization logic in Scala
    pub fn category(&self) -> StepCategory {
        match self {
            DwTableCalculateStep::CalculateStart | DwTableCalculateStep::CalculateEnd => StepCategory::Calculate,
            DwTableCalculateStep::SinkCkStart
            | DwTableCalculateStep::SinkCkEnd
            | DwTableCalculateStep::SinkHdfsStart
            | DwTableCalculateStep::SinkHdfsEnd => StepCategory::Sink,
            DwTableCalculateStep::ValidateStart | DwTableCalculateStep::ValidateEnd => StepCategory::Validate,
            DwTableCalculateStep::TransformStart | DwTableCalculateStep::TransformEnd => StepCategory::Transform,
        }
    }

    /// Get processing order (for sequential execution)
    /// Corresponds to: Step ordering logic in Scala
    pub fn processing_order(&self) -> u8 {
        match self {
            DwTableCalculateStep::ValidateStart => 1,
            DwTableCalculateStep::ValidateEnd => 2,
            DwTableCalculateStep::TransformStart => 3,
            DwTableCalculateStep::TransformEnd => 4,
            DwTableCalculateStep::CalculateStart => 5,
            DwTableCalculateStep::CalculateEnd => 6,
            DwTableCalculateStep::SinkHdfsStart => 7,
            DwTableCalculateStep::SinkHdfsEnd => 8,
            DwTableCalculateStep::SinkCkStart => 9,
            DwTableCalculateStep::SinkCkEnd => 10,
        }
    }
}

impl fmt::Display for DwTableCalculateStep {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.step_name())
    }
}

/// StepCategory represents the different categories of calculation steps
/// Corresponds to: Step categorization in Scala implementation
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum StepCategory {
    /// Data calculation steps
    Calculate,

    /// Data persistence steps
    Sink,

    /// Data validation steps
    Validate,

    /// Data transformation steps
    Transform,
}

impl fmt::Display for StepCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            StepCategory::Calculate => write!(f, "CALCULATE"),
            StepCategory::Sink => write!(f, "SINK"),
            StepCategory::Validate => write!(f, "VALIDATE"),
            StepCategory::Transform => write!(f, "TRANSFORM"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_step_name() {
        assert_eq!(DwTableCalculateStep::CalculateStart.step_name(), "CALCULATE_START");
        assert_eq!(DwTableCalculateStep::CalculateEnd.step_name(), "CALCULATE_END");
        assert_eq!(DwTableCalculateStep::SinkCkStart.step_name(), "SINK_CK_START");
        assert_eq!(DwTableCalculateStep::SinkCkEnd.step_name(), "SINK_CK_END");
    }

    #[test]
    fn test_step_type_checking() {
        assert!(DwTableCalculateStep::CalculateStart.is_start_step());
        assert!(!DwTableCalculateStep::CalculateStart.is_end_step());

        assert!(DwTableCalculateStep::CalculateEnd.is_end_step());
        assert!(!DwTableCalculateStep::CalculateEnd.is_start_step());
    }

    #[test]
    fn test_step_pairing() {
        assert_eq!(DwTableCalculateStep::CalculateStart.get_end_step(), Some(DwTableCalculateStep::CalculateEnd));

        assert_eq!(DwTableCalculateStep::CalculateEnd.get_start_step(), Some(DwTableCalculateStep::CalculateStart));

        assert_eq!(DwTableCalculateStep::SinkCkStart.get_end_step(), Some(DwTableCalculateStep::SinkCkEnd));
    }

    #[test]
    fn test_step_category() {
        assert_eq!(DwTableCalculateStep::CalculateStart.category(), StepCategory::Calculate);
        assert_eq!(DwTableCalculateStep::SinkCkStart.category(), StepCategory::Sink);
        assert_eq!(DwTableCalculateStep::ValidateStart.category(), StepCategory::Validate);
        assert_eq!(DwTableCalculateStep::TransformStart.category(), StepCategory::Transform);
    }

    #[test]
    fn test_processing_order() {
        assert!(
            DwTableCalculateStep::ValidateStart.processing_order()
                < DwTableCalculateStep::CalculateStart.processing_order()
        );
        assert!(
            DwTableCalculateStep::CalculateEnd.processing_order()
                < DwTableCalculateStep::SinkCkStart.processing_order()
        );
    }

    #[test]
    fn test_hash_map_usage() {
        let mut map = HashMap::new();
        map.insert(DwTableCalculateStep::CalculateStart, "start_time");
        map.insert(DwTableCalculateStep::CalculateEnd, "end_time");

        assert!(map.contains_key(&DwTableCalculateStep::CalculateStart));
        assert_eq!(map.get(&DwTableCalculateStep::CalculateStart), Some(&"start_time"));
    }

    #[test]
    fn test_display() {
        assert_eq!(format!("{}", DwTableCalculateStep::CalculateStart), "CALCULATE_START");
        assert_eq!(format!("{}", StepCategory::Calculate), "CALCULATE");
    }
}
