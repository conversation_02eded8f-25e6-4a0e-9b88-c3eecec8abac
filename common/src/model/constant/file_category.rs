use std::str::FromStr;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
#[allow(non_camel_case_types)]
pub enum FileCategory {
    STDF,
    SUMMARY,
    LOT_RELATION,
    BLACKLIST,
    OTHER,
    BIT_MEM,
    RAW_DATA,
    SHMOO,
    LOG,
    MAP,
    ZIP_SPLIT,
    WIP,
    WAT,
}

impl FileCategory {
    pub fn to_str(&self) -> &'static str {
        match self {
            FileCategory::STDF => "STDF",
            FileCategory::SUMMARY => "SUMMARY",
            FileCategory::LOT_RELATION => "LOT_RELATION",
            FileCategory::BLACKLIST => "BLACKLIST",
            FileCategory::OTHER => "OTHER",
            FileCategory::BIT_MEM => "BIT_MEM",
            FileCategory::RAW_DATA => "RAW_DATA",
            FileCategory::SHMOO => "SHMOO",
            FileCategory::LOG => "LOG",
            FileCategory::MAP => "MAP",
            FileCategory::ZIP_SPLIT => "ZIP_SPLIT",
            FileCategory::WIP => "WIP",
            FileCategory::WAT => "WAT",
        }
    }
}

impl ToString for FileCategory {
    fn to_string(&self) -> String {
        self.to_str().to_string()
    }
}

impl FromStr for FileCategory {
    type Err = ();

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "STDF" => Ok(FileCategory::STDF),
            "SUMMARY" => Ok(FileCategory::SUMMARY),
            "LOT_RELATION" => Ok(FileCategory::LOT_RELATION),
            "BLACKLIST" => Ok(FileCategory::BLACKLIST),
            "OTHER" => Ok(FileCategory::OTHER),
            "BIT_MEM" => Ok(FileCategory::BIT_MEM),
            "RAW_DATA" => Ok(FileCategory::RAW_DATA),
            "SHMOO" => Ok(FileCategory::SHMOO),
            "LOG" => Ok(FileCategory::LOG),
            "MAP" => Ok(FileCategory::MAP),
            "ZIP_SPLIT" => Ok(FileCategory::ZIP_SPLIT),
            "WIP" => Ok(FileCategory::WIP),
            "WAT" => Ok(FileCategory::WAT),
            _ => Err(()),
        }
    }
}
