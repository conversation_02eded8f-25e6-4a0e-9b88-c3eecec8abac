pub enum TestState {
    Valid,
    Invalid,
}

impl TestState {
    pub fn from_str(s: &str) -> Self {
        match s {
            "Valid" => TestState::Valid,
            "Invalid" => TestState::Invalid,
            _ => panic!("Invalid test state: {}", s),
        }
    }

    pub fn to_str(&self) -> &str {
        match self {
            TestState::Valid => "Valid",
            TestState::Invalid => "Invalid",
        }
    }

    pub fn to_string(&self) -> String {
        self.to_str().to_string()
    }
}
