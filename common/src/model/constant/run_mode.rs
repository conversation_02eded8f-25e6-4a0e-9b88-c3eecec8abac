#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq, Hash)]
pub enum RunMode {
    STANDALON<PERSON>,
    DISTRIBUTED,
    MANUAL,
    RUST,
}

impl RunMode {
    pub fn new(mode: &str) -> Self {
        match mode {
            "STANDALONE" => RunMode::STAN<PERSON><PERSON>ON<PERSON>,
            "DISTRIBUTED" => RunMode::DISTRIBUTED,
            "MANUAL" => RunMode::MANU<PERSON>,
            "RUST" => RunMode::RUST,
            _ => panic!("Invalid run mode: {}", mode),
        }
    }

    pub fn to_string(&self) -> String {
        match self {
            RunMode::STANDALONE => "STANDALONE".to_string(),
            RunMode::DISTRIBUTED => "DISTRIBUTED".to_string(),
            RunMode::MANUAL => "MANUAL".to_string(),
            RunMode::RUST => "RUST".to_string(),
        }
    }
}
