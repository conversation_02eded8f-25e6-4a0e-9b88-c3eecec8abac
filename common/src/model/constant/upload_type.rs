#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq, Hash)]
#[allow(non_camel_case_types)]
pub enum UploadType {
    MANUAL,
    MANUAL_PRODUCTION,
    AUTO,
}

impl UploadType {
    pub fn new(type_name: &str) -> Self {
        match type_name {
            "MANUAL" => UploadType::MA<PERSON><PERSON>,
            "MANUAL_PRODUCTION" => UploadType::MANUAL_PRODUCTION,
            "AUTO" => UploadType::AUTO,
            _ => panic!("Invalid upload type: {}", type_name),
        }
    }

    pub fn to_string(&self) -> String {
        match self {
            UploadType::MANUAL => "MANUAL".to_string(),
            UploadType::MANUAL_PRODUCTION => "MANUAL_PRODUCTION".to_string(),
            UploadType::AUTO => "AUTO".to_string(),
        }
    }
}
