#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>q, <PERSON><PERSON>, <PERSON>h)]
pub enum TestResult {
    P,
    F,
}

impl TestResult {
    pub fn to_str(&self) -> &str {
        match self {
            TestResult::P => "P",
            TestResult::F => "F",
        }
    }
}

impl ToString for TestResult {
    fn to_string(&self) -> String {
        self.to_str().to_string()
    }
}
