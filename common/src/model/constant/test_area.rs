use std::collections::HashMap;
use std::str::FromStr;

/// TestArea 枚举，对应 Java 中的 TestArea 枚举类
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum TestArea {
    // CP
    CP,
    CPMap,
    CPInklessMap,
    Bump,
    BumpMap,
    Burnin,
    BurninMap,
    WLT,
    WLTMap,
    RelWafer,
    RelMap,
    AssyWafer,
    AssyMap,
    
    // FT
    Assy,
    Rel,
    FT,
    SLT,
    MT,
    NA,
    CAL,
    
    // WAT
    WAT,
}

impl TestArea {
    /// 获取测试区域字符串
    pub fn get_area(&self) -> &'static str {
        match self {
            TestArea::CP => "CP",
            TestArea::CPMap => "CP(Map)",
            TestArea::CPInklessMap => "CP(InklessMap)",
            TestArea::Bump => "BUMP",
            TestArea::BumpMap => "BUMP(Map)",
            TestArea::Burnin => "BURNIN",
            TestArea::BurninMap => "BURNIN(Map)",
            TestArea::WLT => "WLT",
            TestArea::WLTMap => "WLT(Map)",
            TestArea::RelWafer => "REL(Wafer)",
            TestArea::RelMap => "REL(Map)",
            TestArea::AssyWafer => "ASSY(Wafer)",
            TestArea::AssyMap => "ASSY(Map)",
            TestArea::Assy => "ASSY",
            TestArea::Rel => "REL",
            TestArea::FT => "FT",
            TestArea::SLT => "SLT",
            TestArea::MT => "MT",
            TestArea::NA => "NA",
            TestArea::CAL => "CAL",
            TestArea::WAT => "WAT",
        }
    }

    /// 获取测试范围
    pub fn get_test_scope(&self) -> &'static str {
        match self {
            TestArea::CP | TestArea::CPMap | TestArea::CPInklessMap | 
            TestArea::Bump | TestArea::BumpMap | TestArea::Burnin | TestArea::BurninMap |
            TestArea::WLT | TestArea::WLTMap | TestArea::RelWafer | TestArea::RelMap |
            TestArea::AssyWafer | TestArea::AssyMap => "CP",
            
            TestArea::Assy | TestArea::Rel | TestArea::FT | TestArea::SLT |
            TestArea::MT | TestArea::NA | TestArea::CAL => "FT",
            
            TestArea::WAT => "WAT",
        }
    }

    /// 从字符串创建 TestArea
    pub fn of(test_area: &str) -> Option<TestArea> {
        match test_area {
            "CP" => Some(TestArea::CP),
            "CP(Map)" => Some(TestArea::CPMap),
            "CP(InklessMap)" => Some(TestArea::CPInklessMap),
            "BUMP" => Some(TestArea::Bump),
            "BUMP(Map)" => Some(TestArea::BumpMap),
            "BURNIN" => Some(TestArea::Burnin),
            "BURNIN(Map)" => Some(TestArea::BurninMap),
            "WLT" => Some(TestArea::WLT),
            "WLT(Map)" => Some(TestArea::WLTMap),
            "REL(Wafer)" => Some(TestArea::RelWafer),
            "REL(Map)" => Some(TestArea::RelMap),
            "ASSY(Wafer)" => Some(TestArea::AssyWafer),
            "ASSY(Map)" => Some(TestArea::AssyMap),
            "ASSY" => Some(TestArea::Assy),
            "REL" => Some(TestArea::Rel),
            "FT" => Some(TestArea::FT),
            "SLT" => Some(TestArea::SLT),
            "MT" => Some(TestArea::MT),
            "NA" => Some(TestArea::NA),
            "CAL" => Some(TestArea::CAL),
            "WAT" => Some(TestArea::WAT),
            _ => None,
        }
    }

    /// 获取 FT 测试区域列表
    pub fn get_ft_list() -> Vec<TestArea> {
        vec![
            TestArea::Assy,
            TestArea::Rel,
            TestArea::FT,
            TestArea::SLT,
            TestArea::MT,
            TestArea::NA,
            TestArea::CAL,
        ]
    }

    /// 获取 CP 测试区域列表
    pub fn get_cp_list() -> Vec<TestArea> {
        vec![
            TestArea::CP,
            TestArea::CPMap,
            TestArea::CPInklessMap,
            TestArea::Bump,
            TestArea::BumpMap,
            TestArea::Burnin,
            TestArea::BurninMap,
            TestArea::WLT,
            TestArea::WLTMap,
            TestArea::RelWafer,
            TestArea::RelMap,
            TestArea::AssyWafer,
            TestArea::AssyMap,
        ]
    }

    /// 获取 CP Map 数据源列表
    pub fn get_cp_map_data_source_list() -> Vec<TestArea> {
        vec![
            TestArea::CPMap,
            TestArea::BumpMap,
            TestArea::BurninMap,
            TestArea::WLTMap,
            TestArea::RelMap,
            TestArea::AssyMap,
        ]
    }

    /// 获取 CP Inkless Map 数据源列表
    pub fn get_cp_inkless_map_data_source_list() -> Vec<TestArea> {
        vec![TestArea::CPInklessMap]
    }

    /// 按测试范围分组
    pub fn group_by_test_scope() -> HashMap<String, Vec<TestArea>> {
        let mut map = HashMap::new();
        
        map.insert("CP".to_string(), Self::get_cp_list());
        map.insert("FT".to_string(), Self::get_ft_list());
        map.insert("WAT".to_string(), vec![TestArea::WAT]);
        
        map
    }
}

impl FromStr for TestArea {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Self::of(s).ok_or_else(|| format!("Unknown test area: {}", s))
    }
}

impl ToString for TestArea {
    fn to_string(&self) -> String {
        self.get_area().to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cp_list() {
        let cp_list = TestArea::get_cp_list();
        assert!(cp_list.contains(&TestArea::CP));
        assert!(cp_list.contains(&TestArea::CPMap));
        assert!(cp_list.contains(&TestArea::Bump));
        assert!(!cp_list.contains(&TestArea::FT));
    }

    #[test]
    fn test_ft_list() {
        let ft_list = TestArea::get_ft_list();
        assert!(ft_list.contains(&TestArea::FT));
        assert!(ft_list.contains(&TestArea::Assy));
        assert!(!ft_list.contains(&TestArea::CP));
    }

    #[test]
    fn test_of() {
        assert_eq!(TestArea::of("CP"), Some(TestArea::CP));
        assert_eq!(TestArea::of("FT"), Some(TestArea::FT));
        assert_eq!(TestArea::of("UNKNOWN"), None);
    }

    #[test]
    fn test_get_test_scope() {
        assert_eq!(TestArea::CP.get_test_scope(), "CP");
        assert_eq!(TestArea::FT.get_test_scope(), "FT");
        assert_eq!(TestArea::WAT.get_test_scope(), "WAT");
    }
}