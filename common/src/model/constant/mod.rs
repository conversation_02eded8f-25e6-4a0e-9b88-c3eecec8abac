pub mod file_category;
pub mod run_mode;
pub mod test_area;
pub mod test_result;
pub mod test_state;
pub mod upload_type;

// 常量
pub const COMMA: &str = ",";
pub const TAB: &str = "\t";
pub const ENTER: &str = "\n";
pub const EMPTY: &str = "";
pub const EMPTY_STR: &str = EMPTY;
pub const COLON: &str = ":";
pub const SEMICOLON: &str = ";";
pub const PLUS_PLUS: &str = "#";
pub const SPACE: &str = " ";
pub const LOGICAL_OR: &str = "||";
pub const LOGICAL_AND: &str = "&&";
pub const QUESTION_MARK: &str = "?";
pub const BITWISE_AND: &str = "&";
pub const OR: &str = "OR";
pub const AND: &str = "AND";
pub const LEFT_MIDDLE_BRACKET: &str = "[";
pub const RIGHT_MIDDLE_BRACKET: &str = "]";
pub const SINGLE_QUOTATION: &str = "'";
pub const LEFT_CURLY_BRACE: &str = "{";
pub const RIGHT_CURLY_BRACE: &str = "}";
pub const ALL: &str = "ALL";
pub const CM: &str = "cm";
pub const NORMAL_TEST_PREFIX: &str = "P";
pub const RETEST_PREFIX: &str = "R";
pub const ONE: &str = "1";
pub const PASS: &str = "PASS";
pub const FAIL: &str = "FAIL";
pub const SEND_LOG: &str = "SEND_LOG";
pub const ATR: &str = "ATR";
pub const FAR: &str = "FAR";
pub const FTR: &str = "FTR";
pub const HBR: &str = "HBR";
pub const MIR: &str = "MIR";
pub const MRR: &str = "MRR";
pub const PCR: &str = "PCR";
pub const PGR: &str = "PGR";
pub const PIR: &str = "PIR";
pub const PMR: &str = "PMR";
pub const PRR: &str = "PRR";
pub const PTR: &str = "PTR";
pub const SBR: &str = "SBR";
pub const SDR: &str = "SDR";
pub const TSR: &str = "TSR";
pub const WCR: &str = "WCR";
pub const WIR: &str = "WIR";
pub const WRR: &str = "WRR";
pub const MPR: &str = "MPR";
pub const DTR: &str = "DTR";
pub const STR: &str = "STR";
pub const PSR: &str = "PSR";
pub const NMR: &str = "NMR";

// 自定义测项类型
pub const CTDR: &str = "CTDR";

// STR COND_LST field array
pub const SHIFT_FREQ: &str = "SHIFT_FREQ";
pub const CAPTURE_FREQ: &str = "CAPTURE_FREQ";
pub const NMR_REF: &str = "NMR_REF";

// ods类型
pub const TEST_ITEM_DATA: &str = "TEST_ITEM_DATA";
pub const DIE_DATA: &str = "DIE_DATA";
pub const ECID_DATA: &str = "ECID_DATA";
pub const UID_DATA: &str = "UID_DATA";
pub const TEST_ITEM_BIT_MEM_DATA: &str = "TEST_ITEM_BIT_MEM_DATA";
pub const DIE_BIT_MEM_DATA: &str = "DIE_BIT_MEM_DATA";

// TETSITME_TYPE
pub const P: &str = "P";
pub const F: &str = "F";
pub const M: &str = "M";

pub const ODS_WAT: &str = "WAT";
pub const ODS_WAT_TEST_STAGE: &str = "WAT1";
pub const DWD_DIE_DETAIL: &str = "die_detail";
pub const DWD_TEST_ITEM_DETAIL: &str = "test_item_detail";
pub const DIM_TEST_ITEM: &str = "test_item";
pub const DIM_LOT_WAFER: &str = "lot_wafer";
pub const DIM_LOT_WAFER_BIN: &str = "lot_wafer_bin";
pub const DIM_TEST_PROGRAM_SITE: &str = "test_program_site";
pub const DIM_TEST_PROGRAM_BIN: &str = "test_program_bin";
pub const DIM_TEST_PROGRAM_TEST_ITEM: &str = "test_program_test_item";

pub const CUSTOMER: &str = "{CUSTOMER}";
pub const SUB_CUSTOMER: &str = "{SUB_CUSTOMER}";
pub const FACTORY: &str = "{FACTORY}";
pub const FACTORY_SITE: &str = "{FACTORY_SITE}";
pub const TYPE: &str = "{TYPE}";
pub const FILE_CATEGORY: &str = "{FILE_CATEGORY}";
pub const TEST_AREA: &str = "{TEST_AREA}";
pub const DEVICE_ID: &str = "{DEVICE_ID}";
pub const LOT_TYPE: &str = "{LOT_TYPE}";
pub const TEST_STAGE: &str = "{TEST_STAGE}";
pub const LOT_ID: &str = "{LOT_ID}";
pub const SBLOT_ID: &str = "{SBLOT_ID}";
pub const WAFER_ID: &str = "{WAFER_ID}";
pub const FILE_ID: &str = "{FILE_ID}";
pub const WAFER_NO: &str = "{WAFER_NO}";
pub const TABLE: &str = "{TABLE}";
pub const UPLOAD_TYPE: &str = "{UPLOAD_TYPE}";
pub const DT: &str = "{DT}";
pub const DW_LAYER: &str = "{DW_LAYER}";
pub const DATA_SOURCE: &str = "{DATA_SOURCE}";
pub const LOT_BUCKET: &str = "{LOT_BUCKET}";
pub const SMALL_LETTER_ODS_DATA_TYPE: &str = "{SMALL_LETTER_ODS_DATA_TYPE}";
pub const UNKNOWN: &str = "unknown";

pub const FAB: &str = "{FAB}";
pub const U: &str = "U";
pub const DB_NAME: &str = "{DB_NAME}";
pub const REPLACE_NAME: &str = "{REPLACE}";
pub const REPLACE_TABLE: &str = "_replace";
pub const CLUSTER_NAME: &str = "{CLUSTER}";
pub const CLUSTER_TABLE: &str = "_cluster";
pub const LOCAL_TABLE: &str = "_local";
pub const TABLE_NAME: &str = "{TABLE_NAME}";
pub const PARTITION: &str = "{PARTITION}";
pub const IN_PARTITION: &str = "{IN_PARTITION}";
pub const CREATE_DAY_KEY: &str = "{CREATE_DAY_KEY}";
pub const FUNCTION: &str = "FUNCTION";
pub const REMOTE: &str = "remote";

pub const KEY_FIELD: &str = "{KEY_FIELD}";

pub const WHITE_SPACE: &str = " ";
pub const SPLIT_POINT: &str = "\\.";
pub const SPLIT_LOGICAL_OR: &str = "\\|\\|";
pub const SPLIT_TAB: &str = "\\t";
pub const SPLIT_QUESTION_MARK: &str = "\\?";
pub const MIDDLE_LINE: &str = "-";
pub const UNDER_LINE: &str = "_";
pub const PERCENT: &str = "%";
pub const MULTIPLICATION_SIGN: &str = "*";
pub const WAVY_LINE: &str = "~";
pub const FAB_3: &str = "Fab3";
pub const FAB_6: &str = "Fab6";
pub const FAB_10: &str = "Fab10";
pub const REDIS_KEY_FILE_LOCK: &str = "LOCK";
pub const ZERO: &str = "0";
pub const POINT: &str = ".";
pub const SLASH: &str = "/";
pub const PERCENTAGE: &str = "%";
pub const SPLIT_LEFT_SMALL_BRACKETS: &str = "\\(";
pub const LEFT_SMALL_BRACKETS: &str = "(";
pub const RIGHT_SMALL_BRACKETS: &str = ")";
pub const SYSTEM: &str = "System";
pub const DEFAULT_VERSION: i32 = 1;
pub const BIN: &str = "bin";
pub const STANDARD_BIN_NAME_PREFIX: &str = "BIN";

pub const CRC_SUFFIX: &str = "crc";

pub const EQUAL_SIGN: &str = "=";
pub const FT_SUMMARY_BIN_REFER: &str = "java.util.List<com.guwave.onedata.upload.model.dto.BinInfo>";
pub const CP_SUMMARY_BIN_REFER: &str = "java.util.List<com.guwave.onedata.source.common.model.stdf.BinInfo>";
pub const SBIN_NAME_PREFIX: &str = "SBIN";
pub const HBIN_NAME_PREFIX: &str = "HBIN";
pub const BIN_STATUS_RECOVER: &str = "recover";
pub const BIN_STATUS_UNCHANGE: &str = "unchange";
pub const BIN_STATUS_EXCHANGE: &str = "exchange";
pub const NORMAL_OFFLINE_PREFIX: &str = "P";
pub const RETEST_OFFLINE_PREFIX: &str = "R";
pub const INTERRUPT_PREFIX: &str = "I";
pub const DUPRETEST_PREFIX: &str = "R";

pub const DIM_TEST_PROGRAM_NOT_NULL_DEFAULT: i64 = 999999999;
pub const DIM_TEST_PROGRAM_VERSION_MAX: i64 = 3999999999;
pub const DIM_TEST_PROGRAM_PARTITION_TEMPLATE: &str = "('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}')";
pub const DIM_LOT_WAFER_PARTITION_TEMPLATE: &str = "('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{SUB_CUSTOMER}')";
pub const DIM_TEST_ITEM_PARTITION_TEMPLATE: &str =
    "('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{SUB_CUSTOMER}','{DEVICE_ID}')";
pub const DWD_WIP_DETAIL_PARTITION_TEMPLATE: &str = "('{CUSTOMER}','WIP','{FACTORY}','{TEST_AREA}','{DEVICE_ID}')";
pub const OPTIMIZE_TABLE_TEMPLATE: &str = "optimize table {DB_NAME}.{TABLE_NAME} partition {PARTITION} final";

// python arg type
pub const PYTHON_ARG_TYPE_LOG: &str = "log";
pub const PYTHON_ARG_TYPE_BIN: &str = "bin";
// Ecid rule type
pub const HEX_ASCII: &str = "HexAscii";
pub const CONVERT36: &str = "Convert36";
// ecid check type
pub const EFUSE: &str = "EFUSE";
pub const CONFIG: &str = "CONFIG";
// module
pub const DATAWARE_SCHEDULER: &str = "dataware-scheduler";
pub const DATAWARE_COLLECTX: &str = "dataware-collectx";
pub const DATAWARE_SOURCE: &str = "dataware-source";

// SINK_TYPE
pub const HDFS_SINK: &str = "HDFSSINK";
pub const KAFKA_SINK: &str = "KAFKASINK";
// Hdfs Partition
pub const CP_HDFS_PARTITION: [&str; 5] = ["TEST_AREA", "CUSTOMER", "FACTORY", "LOT_ID", "WAFER_NO"];
pub const FT_HDFS_PARTITION: [&str; 4] = ["TEST_AREA", "CUSTOMER", "FACTORY", "LOT_ID"];

pub const EMPTY_JSON_ARRAY: &str = "[]";
pub const EMPTY_JSON: &str = "{}";

/// hdfs模式
pub const HDFS_MODE_HA: &str = "HA";
pub const HDFS_MODE_STANDALONE: &str = "STANDALONE";

pub const NEXT_STEP: &str = "NEXT_STEP";
pub const DEFAULT_MANUAL_FLOW_ID: &str = "P1";

pub const TEST_RAW_DATA: &str = "Test Raw Data";
pub const CP_MAP: &str = "CP Map";
pub const INKLESS_MAP: &str = "Inkless Map";
pub const MES: &str = "MES";
pub const JS_ENGINE_NAME: &str = "javascript";
pub const SCRIPT: &str = "{SCRIPT}";
// FIELD
pub const FIELD_FILE_ID: &str = "FILE_ID";
// Manual Convert
pub const MANUAL_CONVERT_SUCCESS: &str = "00000001";
pub const ORG: &str = "ORG";
pub const NEW: &str = "NEW";
pub const REMOVE_FLYING_POINT_STRATEGY_WAFER_CNT: &str = "WAFER_CNT";
pub const REMOVE_FLYING_POINT_STRATEGY_QUALTILE: &str = "QUALTILE";
pub const DUP_RETEST: &str = "DUP_RETEST";

pub const PLACEHOLDER_CUSTOMER: &str = "#{CUSTOMER}";
pub const PLACEHOLDER_SUB_CUSTOMER: &str = "#{SUB_CUSTOMER}";
pub const PLACEHOLDER_FACTORY: &str = "#{FACTORY}";
pub const PLACEHOLDER_FACTORY_SITE: &str = "#{FACTORY_SITE}";
pub const PLACEHOLDER_DEVICE_ID: &str = "#{DEVICE_ID}";
pub const PLACEHOLDER_TEST_AREA: &str = "#{TEST_AREA}";
pub const PLACEHOLDER_LOT_ID: &str = "#{LOT_ID}";
pub const PLACEHOLDER_WAFER_NO: &str = "#{WAFER_NO}";
pub const PLACEHOLDER_TEST_STAGE: &str = "#{TEST_STAGE}";
pub const PLACEHOLDER_LOT_TYPE: &str = "#{LOT_TYPE}";
pub const PLACEHOLDER_FILE_CATEGORY: &str = "#{FILE_CATEGORY}";
pub const PLACEHOLDER_VERSION: &str = "#{VERSION}";
pub const PLACEHOLDER_DATA_VERSION: &str = "#{DATA_VERSION}";
pub const DEFAULT_TIME_FORMAT: &str = "yyyy-MM-dd HH:mm:ss";
pub const PARTITION_EXPR_PREFIX: &str = "IN PARTITION ";
pub const LOCAL_FILE_PREFIX: &str = "file://";
pub const MINIO_FILE_PREFIX: &str = "s3a://";
pub const CRC_FILE_SUFFIX: &str = ".crc";
pub const PART_PREFIX: &str = "part-";
pub const PARQUET_FILE_SUFFIX: &str = ".parquet";
pub const DEFAULT_TEST_PROGRAM_SUFFIX: &str = "Program_";
pub const CURRENT_TIME: &str = "{CURRENT_TIME}";
pub const CREATE_TIME: &str = "CREATE_TIME";
pub const PAGE: &str = "{PAGE}";
pub const PAGE_SIZE: &str = "{PAGE_SIZE}";
pub const SINGLE_RUN_MODE: &str = "SINGLE";

pub const ADS_DB_NAME: &str = "{ADS_DB_NAME}";
pub const DATABASE: &str = "{DATABASE}";
pub const DWD_DB_NAME: &str = "{DWD_DB_NAME}";
pub const META_DB_NAME: &str = "{META_DB_NAME}";
pub const ODS_DB_NAME: &str = "{ODS_DB_NAME}";
pub const SUPPORT_TEST_AREA_CP: &str = "CP";
pub const SUPPORT_TEST_AREA_CP_MAP: &str = "CP(Map)";
pub const SUPPORT_TEST_AREA_CP_INKLESS_MAP: &str = "CP(InklessMap)";
pub const RULE_EXPRESSION: &str = "{RULE_EXPRESSION}";

// PASS/FAIL
pub const PF_PASS: &str = "P";
pub const PF_FAIL: &str = "F";

