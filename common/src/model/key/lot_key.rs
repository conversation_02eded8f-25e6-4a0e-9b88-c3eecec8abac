use serde::{Deserialize, Serialize};

/// <PERSON><PERSON><PERSON> represents the unique identifier for a lot in the data processing pipeline
/// Corresponds to: LotKey case class in Scala implementation
/// 
/// This structure is used to identify unique lot processing contexts and for Redis key generation
#[derive(Debug, <PERSON><PERSON>, <PERSON>h, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct LotKey {
    /// Customer name - Corresponds to Scala LotKey.customer
    pub customer: String,
    
    /// Sub-customer name - Corresponds to Scala LotKey.subCustomer
    pub sub_customer: String,
    
    /// Test area (CP, FT, etc.) - Corresponds to Scala LotKey.testArea
    pub test_area: String,
    
    /// Factory name - Corresponds to Scala LotKey.factory
    pub factory: String,
    
    /// Factory site location - Corresponds to Scala LotKey.factorySite
    pub factory_site: String,
    
    /// Device identifier - Corresponds to Scala LotKey.deviceId
    pub device_id: String,

    /// Lot id identifier - Corresponds to Scala LotKey.lotId
    pub lot_id: String,
    
    /// Test stage within the test area - Corresponds to Scala LotKey.testStage
    pub test_stage: String,
    
    /// Lot type (PRODUCTION, ENGINEERING, etc.) - Corresponds to Scala LotKey.lotType
    pub lot_type: String,
}

impl LotKey {
    /// Create a new LotKey instance
    /// Corresponds to: LotKey case class constructor in Scala
    pub fn new(
        customer: String,
        sub_customer: String,
        test_area: String,
        factory: String,
        factory_site: String,
        device_id: String,
        lot_id: String,
        test_stage: String,
        lot_type: String,
    ) -> Self {
        Self {
            customer,
            sub_customer,
            test_area,
            factory,
            factory_site,
            device_id,
            lot_id,
            test_stage,
            lot_type,
        }
    }

    /// Generate a string representation for logging and debugging
    /// Corresponds to: LotKey.toString in Scala
    pub fn to_string_key(&self) -> String {
        format!(
            "{}_{}_{}_{}_{}_{}_{}_{}_{}",
            self.customer,
            self.sub_customer,
            self.test_area,
            self.factory,
            self.factory_site,
            self.device_id,
            self.lot_id,
            self.test_stage,
            self.lot_type
        )
    }
    /// Generate a path-safe string representation
    /// Used for path generation in data processing pipelines
    pub fn to_path_string(&self) -> String {
        format!(
            "TEST_AREA={}/CUSTOMER={}/FACTORY={}/DEVICE_ID={}/LOT_ID={}/TEST_STAGE={}/LOT_TYPE={}",
            self.test_area,
            self.customer,
            self.factory,
            self.device_id,
            self.lot_id,
            self.test_stage,
            self.lot_type
        )
    }

    /// Generate a compact identifier for the lot
    /// Useful for short identifiers in logs or UI
    pub fn to_compact_id(&self) -> String {
        format!("{}_{}_{}_{}", self.customer, self.factory, self.device_id, self.test_stage)
    }

    /// Check if this lot belongs to the same customer and factory as another lot
    pub fn same_customer_factory(&self, other: &LotKey) -> bool {
        self.customer == other.customer
            && self.factory == other.factory
            && self.device_id == other.device_id
    }
}

impl std::fmt::Display for LotKey {
    /// Display implementation for logging
    /// Corresponds to: LotKey logging in Scala
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "LotKey[customer={}, factory={}, device_id={}, lot_id={}, test_stage={}, lot_type={}]",
            self.customer, self.factory, self.device_id, self.lot_id, self.test_stage, self.lot_type
        )
    }
}

impl Default for LotKey {
    fn default() -> Self {
        Self {
            customer: String::new(),
            sub_customer: String::new(),
            test_area: String::new(),
            factory: String::new(),
            factory_site: String::new(),
            device_id: String::new(),
            lot_id: String::new(),
            test_stage: String::new(),
            lot_type: String::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_lot_key_creation() {
        let lot_key = LotKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "CP".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "CP1".to_string(),
            "PRODUCTION".to_string(),
        );

        assert_eq!(lot_key.customer, "YEESTOR");
        assert_eq!(lot_key.test_area, "CP");
        assert_eq!(lot_key.device_id, "YS8293ENAB");
    }

    #[test]
    fn test_lot_key_hash_map_usage() {
        let lot_key1 = LotKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "CP".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "CP1".to_string(),
            "PRODUCTION".to_string(),
        );

        let lot_key2 = lot_key1.clone();

        let mut map = HashMap::new();
        map.insert(lot_key1, "test_value");

        assert!(map.contains_key(&lot_key2));
        assert_eq!(map.get(&lot_key2), Some(&"test_value"));
    }

    #[test]
    fn test_lot_key_display() {
        let lot_key = LotKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "CP".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "CP1".to_string(),
            "PRODUCTION".to_string(),
        );

        let display_str = format!("{}", lot_key);
        assert!(display_str.contains("YEESTOR"));
        assert!(display_str.contains("YS8293ENAB"));
        assert!(display_str.contains("CP1"));
    }

    #[test]
    fn test_lot_key_path_string() {
        let lot_key = LotKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "CP".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "CP1".to_string(),
            "PRODUCTION".to_string(),
        );

        let path_str = lot_key.to_path_string();
        assert!(path_str.contains("TEST_AREA=CP"));
        assert!(path_str.contains("CUSTOMER=YEESTOR"));
        assert!(path_str.contains("DEVICE_ID=YS8293ENAB"));
        assert!(path_str.contains("TEST_STAGE=CP1"));
    }

    #[test]
    fn test_lot_key_string_key() {
        let lot_key = LotKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "CP".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "CP1".to_string(),
            "PRODUCTION".to_string(),
        );

        let string_key = lot_key.to_string_key();
        assert_eq!(
            string_key,
            "YEESTOR_YEESTOR_SUB_CP_LEADYO_LEADYO_SITE_YS8293ENAB_LOT_1_CP1_PRODUCTION"
        );
    }

    #[test]
    fn test_lot_key_compact_id() {
        let lot_key = LotKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "CP".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "CP1".to_string(),
            "PRODUCTION".to_string(),
        );

        let compact_id = lot_key.to_compact_id();
        assert_eq!(compact_id, "YEESTOR_LEADYO_YS8293ENAB_CP1");
    }

    #[test]
    fn test_lot_key_same_customer_factory() {
        let lot_key1 = LotKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "CP".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "CP1".to_string(),
            "PRODUCTION".to_string(),
        );

        let lot_key2 = LotKey::new(
            "YEESTOR".to_string(),
            "YEESTOR_SUB".to_string(),
            "FT".to_string(), // Different test area
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "FT1".to_string(), // Different test stage
            "ENGINEERING".to_string(), // Different lot type
        );

        let lot_key3 = LotKey::new(
            "OTHER_CUSTOMER".to_string(), // Different customer
            "OTHER_SUB".to_string(),
            "CP".to_string(),
            "LEADYO".to_string(),
            "LEADYO_SITE".to_string(),
            "YS8293ENAB".to_string(),
            "LOT_1".to_string(),
            "CP1".to_string(),
            "PRODUCTION".to_string(),
        );

        assert!(lot_key1.same_customer_factory(&lot_key2)); // Same customer/factory/device
        assert!(!lot_key1.same_customer_factory(&lot_key3)); // Different customer
    }
}