use crate::dto::dwd::die_detail_parquet::DieDetailParquet;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::dto::dws::bin_failitem_index::BinFailitemIndex;
use crate::dto::dws::item_order::ItemOrder;
use crate::dto::dws::sub_bin_failitem::SubBinFailitem;
use std::collections::HashMap;
use crate::dto::dwd::file_detail::FileDetail;
use crate::dws::model::key::file_bin_key::FileBinKey;
use crate::dws::model::key::file_die::FileDie;
use crate::dws::table::bin_failitem_index_common_service::BinFailitemIndexCommonService;

const PF_PASS: &str = "P";
const EMPTY: &str = "";




/// Key structure for grouping test item details
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct TestItemKey {
    pub file_id: Option<i64>,
    pub c_part_id: Option<i64>,
    pub ecid: Option<String>,
}

impl TestItemKey {
    pub fn new(file_id: Option<i64>, c_part_id: Option<i64>, ecid: Option<String>) -> Self {
        Self {
            file_id,
            c_part_id,
            ecid,
        }
    }
}

#[derive(Debug, Clone)]
pub struct BinFailitemIndexService {
    pub test_area: String,
    pub common_service: BinFailitemIndexCommonService,
}

impl BinFailitemIndexService {
    pub fn new(test_area: String) -> Self {
        Self {
            test_area,
            common_service: BinFailitemIndexCommonService::new(),
        }
    }

    /// Calculate SubBinFailitem from die details and test item details
    /// Corresponds to calculate() in Scala
    pub fn calculate(
        &self,
        die_detail: &Vec<DieDetailParquet>,
        test_item_detail: &Vec<SubTestItemDetail>,
        file_detail_map: &HashMap<u64, FileDetail>
    ) -> Vec<SubBinFailitem> {
        // Filter die details for passing bins and create file to min pass die map
        let file_to_min_pass_die_map: HashMap<i64, (i64, i64)> = die_detail
            .iter()
            .filter(|d| d.HBIN_PF.as_deref() == Some(PF_PASS))
            .fold(HashMap::new(), |mut acc, die| {
                if let Some(file_id) = die.FILE_ID {
                    let mut group = acc.entry(file_id).or_insert_with(Vec::new);
                    group.push(die.clone());
                }
                acc
            })
            .into_iter()
            .filter_map(|(file_id, dies)| {
                BinFailitemIndexCommonService::generate_file_to_min_pass_die_map(&dies)
                    .map(|result| (file_id, result))
            })
            .collect();

        // Create file to item order map
        let file_to_item_order_map: HashMap<i64, Vec<ItemOrder>> = test_item_detail
            .iter()
            .filter(|t| {
                if let Some(file_id) = t.FILE_ID {
                    if let Some((_, c_part_id)) = file_to_min_pass_die_map.get(&file_id) {
                        return t.C_PART_ID == Some(*c_part_id);
                    }
                }
                false
            })
            .fold(HashMap::new(), |mut acc, item| {
                if let Some(file_id) = item.FILE_ID {
                    acc.entry(file_id).or_insert_with(Vec::new).push(item.clone());
                }
                acc
            })
            .into_iter()
            .map(|(file_id, items)| {
                let (_, item_orders) = 
                    BinFailitemIndexCommonService::generate_file_to_item_order_map(file_id, &items);
                (file_id, item_orders)
            })
            .collect();

        // Group test item details by (FILE_ID, C_PART_ID, ECID) and calculate SubBinFailitem
        let mut grouped_test_items: HashMap<TestItemKey, Vec<SubTestItemDetail>> = HashMap::new();
        for item in test_item_detail {
            let key = TestItemKey::new(item.FILE_ID, item.C_PART_ID, item.ECID.clone());
            grouped_test_items
                .entry(key)
                .or_insert_with(Vec::new)
                .push(item.clone());
        }

        let mut results = Vec::new();
        for (key, items) in grouped_test_items {
            let item_orders = key
                .file_id
                .and_then(|file_id| file_to_item_order_map.get(&file_id))
                .cloned()
                .unwrap_or_default();

            if let Some(sub_bin_failitem) = 
                BinFailitemIndexCommonService::calculate_sub_bin_failitem_index(&items, &item_orders, file_detail_map)
            {
                results.push(sub_bin_failitem);
            }
        }

        results
    }

    /// Calculate BinFailitemIndex for CP (Control Program) mode
    /// Corresponds to cpCalculate() in Scala
    pub fn cp_calculate(&self, sub_bin_failitems: &Vec<SubBinFailitem>) -> Vec<BinFailitemIndex> {
        // Group by FileDie (FILE_ID, ECID)
        let mut grouped_by_file_die: HashMap<FileDie, Vec<SubBinFailitem>> = HashMap::new();
        for item in sub_bin_failitems {
            let key = FileDie::new(item.FILE_ID, item.ECID.clone());
            grouped_by_file_die
                .entry(key)
                .or_insert_with(Vec::new)
                .push(item.clone());
        }

        // Generate final flags for each group
        let mut final_flag_records: Vec<(SubBinFailitem, i32)> = Vec::new();
        for (_, mut records) in grouped_by_file_die {
            let flags = BinFailitemIndexCommonService::generate_cp_final_flag(&mut records);
            final_flag_records.extend(flags);
        }

        // Group by FileBinKey
        let mut grouped_by_file_bin: HashMap<FileBinKey, Vec<SubBinFailitem>> = HashMap::new();
        for (record, final_flag) in final_flag_records {
            let key = FileBinKey::new(
                record.FILE_ID,
                record.HBIN_NUM,
                record.HBIN_NAM.clone(),
                record.HBIN_PF.clone(),
                record.SBIN_NUM,
                record.SBIN_NAM.clone(),
                record.SBIN_PF.clone(),
                final_flag,
            );
            grouped_by_file_bin
                .entry(key)
                .or_insert_with(Vec::new)
                .push(record);
        }

        // Calculate BinFailitemIndex for each group
        let mut results = Vec::new();
        for (key, records) in grouped_by_file_bin {
            if let Some(bin_failitem_index) = 
                BinFailitemIndexCommonService::calculate_bin_failitem_index(&records, key.final_flag)
            {
                results.push(bin_failitem_index);
            }
        }

        results
    }

    /// Calculate BinFailitemIndex for FT (Final Test) mode
    /// Corresponds to ftCalculate() in Scala
    pub fn ft_calculate(&self, sub_bin_failitems: &Vec<SubBinFailitem>) -> Vec<BinFailitemIndex> {
        // Map each SubBinFailitem to (SubBinFailitem, 1) and group by FileBinKey
        let mut grouped_by_file_bin: HashMap<FileBinKey, Vec<SubBinFailitem>> = HashMap::new();
        for item in sub_bin_failitems {
            let key = FileBinKey::new(
                item.FILE_ID,
                item.HBIN_NUM,
                item.HBIN_NAM.clone(),
                item.HBIN_PF.clone(),
                item.SBIN_NUM,
                item.SBIN_NAM.clone(),
                item.SBIN_PF.clone(),
                1, // final_flag is always 1 for FT mode
            );
            grouped_by_file_bin
                .entry(key)
                .or_insert_with(Vec::new)
                .push(item.clone());
        }

        // Calculate BinFailitemIndex for each group
        let mut results = Vec::new();
        for (key, records) in grouped_by_file_bin {
            if let Some(mut bin_failitem_index) =
                BinFailitemIndexCommonService::calculate_bin_failitem_index(&records, key.final_flag)
            {
                // Collect distinct wafer information
                let wafer_ids: Vec<String> = records
                    .iter()
                    .filter_map(|r| r.WAFER_ID.clone())
                    .collect::<std::collections::HashSet<_>>()
                    .into_iter()
                    .collect();
                
                let wafer_nos: Vec<String> = records
                    .iter()
                    .filter_map(|r| r.WAFER_NO.clone())
                    .collect::<std::collections::HashSet<_>>()
                    .into_iter()
                    .collect();
                
                let wafer_lot_ids: Vec<String> = records
                    .iter()
                    .filter_map(|r| r.WAFER_LOT_ID.clone())
                    .collect::<std::collections::HashSet<_>>()
                    .into_iter()
                    .collect();

                // Update wafer information with distinct values
                bin_failitem_index.WAFER_ID = if wafer_ids.is_empty() {
                    None
                } else {
                    Some(wafer_ids.join(","))
                };
                
                bin_failitem_index.WAFER_NO = if wafer_nos.is_empty() {
                    None
                } else {
                    Some(wafer_nos.join(","))
                };
                
                bin_failitem_index.WAFER_LOT_ID = if wafer_lot_ids.is_empty() {
                    None
                } else {
                    Some(wafer_lot_ids.join(","))
                };

                // Set key fields to empty as per Scala logic
                bin_failitem_index.WAFER_ID_KEY = Some(EMPTY.to_string());
                bin_failitem_index.WAFER_NO_KEY = Some(EMPTY.to_string());

                results.push(bin_failitem_index);
            }
        }

        results
    }

    /// Helper function to create distinct comma-separated string from vector
    fn mk_string_distinct(values: Vec<Option<String>>) -> Option<String> {
        let distinct_values: std::collections::HashSet<String> = values
            .into_iter()
            .filter_map(|v| v)
            .collect();
        
        if distinct_values.is_empty() {
            None
        } else {
            Some(distinct_values.into_iter().collect::<Vec<_>>().join(","))
        }
    }
}
