use std::collections::HashMap;
use chrono::{DateTime, Utc};
use serde_json;

use crate::dto::dws::bin_failitem::BinFailitem;
use crate::dto::dws::sub_bin_failitem::SubBinFailitem;
use crate::dto::dws::bin_key::BinKey;
use crate::dto::dws::bin_relation::BinRelation;
use crate::dto::dws::test_program_testItem_bin_relation::TestProgramTestItemBinRelation;
use crate::dws::model::mysql::dw_test_program_test_plan::DwTestProgramTestPlan;
use crate::model::constant::{COMMA, EMPTY_JSON_ARRAY, EMPTY};

#[derive(Debug, Clone)]
pub struct BinFailitemCommonService;

impl BinFailitemCommonService {
    pub fn new() -> Self {
        Self
    }

    /// Calculate BinFailitem from SubBinFailitem
    /// Equivalent to calculateBinFailitem in Scala
    pub fn calculate_bin_failitem(&self, item: &SubBinFailitem) -> BinFailitem {
        let now = chrono::Utc::now().timestamp_millis();
        let create_hour_key = self.get_day_hour(now);
        let create_day_key = self.get_day(now);
        
        let first_fail_item = item.FIRST_FAIL_ITEM.as_ref();
        
        BinFailitem {
            customer: item.CUSTOMER.clone(),
            sub_customer: item.SUB_CUSTOMER.clone(),
            upload_type: item.UPLOAD_TYPE.clone(),
            factory: item.FACTORY.clone(),
            factory_site: item.FACTORY_SITE.clone(),
            fab: item.FAB.clone(),
            fab_site: item.FAB_SITE.clone(),
            test_area: item.TEST_AREA.clone(),
            test_stage: item.TEST_STAGE.clone(),
            device_id: item.DEVICE_ID.clone(),
            lot_type: item.LOT_TYPE.clone(),
            test_program: item.TEST_PROGRAM.clone(),
            test_program_version: item.TEST_PROGRAM_VERSION.clone(),
            test_num: first_fail_item.and_then(|f| f.TEST_NUM),
            test_txt: first_fail_item.and_then(|f| f.TEST_TXT.clone()),
            test_item: first_fail_item.and_then(|f| f.TEST_ITEM.clone()),
            testitem_type: first_fail_item.and_then(|f| f.TESTITEM_TYPE.clone()),
            units: first_fail_item.and_then(|f| f.UNITS.clone()),
            origin_units: first_fail_item.and_then(|f| f.ORIGIN_UNITS.clone()),
            hbin_num: item.HBIN_NUM,
            hbin_num_key: item.HBIN_NUM.map(|n| n.to_string()),
            hbin_nam: item.HBIN_NAM.clone(),
            hbin_pf: item.HBIN_PF.clone(),
            hbin: item.HBIN.clone(),
            sbin_num: item.SBIN_NUM,
            sbin_num_key: item.SBIN_NUM.map(|n| n.to_string()),
            sbin_nam: item.SBIN_NAM.clone(),
            sbin_pf: item.SBIN_PF.clone(),
            sbin: item.SBIN.clone(),
            create_hour_key: Some(create_hour_key),
            create_day_key: Some(create_day_key),
            create_time: Some(now),
            create_user: Some("SYSTEM".to_string()),
            version: Some(now),
            upload_time: item.UPLOAD_TIME,
        }
    }

    /// Calculate bin relation from BinFailitem records
    /// Equivalent to calBinRelation in Scala
    pub fn cal_bin_relation(&self, records: &[BinFailitem]) -> Option<TestProgramTestItemBinRelation> {
        if records.is_empty() {
            return None;
        }
        
        let head = &records[0];
        let bin_relations: Vec<BinRelation> = records.iter().map(|t| {
            BinRelation {
                hbin_num: t.hbin_num,
                hbin_nam: t.hbin_nam.clone(),
                hbin_pf: t.hbin_pf.clone(),
                hbin: t.hbin.clone(),
                sbin_num: t.sbin_num,
                sbin_nam: t.sbin_nam.clone(),
                sbin_pf: t.sbin_pf.clone(),
                sbin: t.sbin.clone(),
            }
        }).collect();
        
        let bin_relation_map = self.convert_to_bin_relation_map(&bin_relations);
        
        Some(TestProgramTestItemBinRelation {
            fab: head.fab.clone(),
            fab_site: head.fab_site.clone(),
            test_program: head.test_program.clone(),
            test_num: head.test_num,
            test_txt: head.test_txt.clone(),
            test_item: head.test_item.clone(),
            testitem_type: head.testitem_type.clone(),
            bin_relation: Some(bin_relation_map),
        })
    }

    /// Convert BinRelation array to HashMap with BinKey
    /// Equivalent to convertToBinRelationMap in Scala
    pub fn convert_to_bin_relation_map(&self, bin_relations: &[BinRelation]) -> HashMap<BinKey, BinRelation> {
        bin_relations.iter().map(|t| {
            let key = BinKey {
                sbin_num: t.sbin_num,
                sbin_nam: t.sbin_nam.clone(),
                hbin_num: t.hbin_num,
                hbin_nam: t.hbin_nam.clone(),
            };
            (key, t.clone())
        }).collect()
    }

    /// Sort and merge SBIN values
    /// Equivalent to sortSbinAndMerge in Scala
    pub fn sort_sbin_and_merge(&self, bin_relations: &[BinRelation]) -> String {
        let mut with_num: Vec<_> = bin_relations.iter()
            .filter(|br| br.sbin_num.is_some())
            .collect();
        with_num.sort_by_key(|br| br.sbin_num);
        
        let mut without_num: Vec<_> = bin_relations.iter()
            .filter(|br| br.sbin_num.is_none())
            .collect();
        without_num.sort_by(|a, b| a.sbin.cmp(&b.sbin));
        
        let mut result: Vec<String> = Vec::new();
        
        // Add sorted items with sbin_num
        for br in with_num {
            if let Some(ref sbin) = br.sbin {
                result.push(sbin.clone());
            }
        }
        
        // Add sorted items without sbin_num
        for br in without_num {
            if let Some(ref sbin) = br.sbin {
                result.push(sbin.clone());
            }
        }
        
        // Remove duplicates while preserving order
        let mut unique_result = Vec::new();
        for item in result {
            if !unique_result.contains(&item) {
                unique_result.push(item);
            }
        }
        
        unique_result.join(COMMA)
    }

    /// Sort and merge HBIN values
    /// Equivalent to sortHbinAndMerge in Scala
    pub fn sort_hbin_and_merge(&self, bin_relations: &[BinRelation]) -> String {
        let mut with_num: Vec<_> = bin_relations.iter()
            .filter(|br| br.hbin_num.is_some())
            .collect();
        with_num.sort_by_key(|br| br.hbin_num);
        
        let mut without_num: Vec<_> = bin_relations.iter()
            .filter(|br| br.hbin_num.is_none())
            .collect();
        without_num.sort_by(|a, b| a.hbin.cmp(&b.hbin));
        
        let mut result: Vec<String> = Vec::new();
        
        // Add sorted items with hbin_num
        for br in with_num {
            if let Some(ref hbin) = br.hbin {
                result.push(hbin.clone());
            }
        }
        
        // Add sorted items without hbin_num
        for br in without_num {
            if let Some(ref hbin) = br.hbin {
                result.push(hbin.clone());
            }
        }
        
        // Remove duplicates while preserving order
        let mut unique_result = Vec::new();
        for item in result {
            if !unique_result.contains(&item) {
                unique_result.push(item);
            }
        }
        
        unique_result.join(COMMA)
    }

    /// Parse existing bin relation from JSON string
    /// Equivalent to parseExistsBinRelation in Scala
    pub fn parse_exists_bin_relation(&self, bin_relation: Option<&String>) -> HashMap<BinKey, BinRelation> {
        match bin_relation {
            Some(json_str) if !json_str.trim().is_empty() => {
                match serde_json::from_str::<Vec<BinRelation>>(json_str) {
                    Ok(relations) => self.convert_to_bin_relation_map(&relations),
                    Err(_) => HashMap::new(),
                }
            },
            _ => HashMap::new(),
        }
    }

    /// Merge existing and new bin relations
    pub fn merge_bin_relations(
        &self, 
        existing: HashMap<BinKey, BinRelation>, 
        new: HashMap<BinKey, BinRelation>
    ) -> Vec<BinRelation> {
        let mut merged = existing;
        for (key, value) in new {
            merged.insert(key, value);
        }
        merged.into_values().collect()
    }

    /// Create new DwTestProgramTestPlan from TestProgramTestItemBinRelation
    pub fn create_test_program_test_plan(
        &self,
        relation: &TestProgramTestItemBinRelation,
        customer: Option<String>,
        sub_customer: Option<String>,
        upload_type: Option<String>,
        test_area: Option<String>,
        factory: Option<String>,
        factory_site: Option<String>,
        device_id: Option<String>,
        test_stage: Option<String>,
        lot_type: Option<String>,
    ) -> DwTestProgramTestPlan {
        let now = chrono::Utc::now().naive_utc();
        let bin_relations: Vec<BinRelation> = relation.bin_relation
            .as_ref()
            .map(|map| map.values().cloned().collect())
            .unwrap_or_default();
        
        let bin_relation_json = serde_json::to_string(&bin_relations).unwrap_or_else(|_| EMPTY_JSON_ARRAY.to_string());
        let hbins = self.sort_hbin_and_merge(&bin_relations);
        let sbins = self.sort_sbin_and_merge(&bin_relations);
        
        DwTestProgramTestPlan {
            id: None,
            customer,
            sub_customer,
            upload_type,
            test_area,
            factory,
            factory_site,
            fab: relation.fab.clone(),
            fab_site: relation.fab_site.clone(),
            device_id,
            test_stage,
            lot_type,
            test_program: relation.test_program.clone(),
            test_item: relation.test_item.clone(),
            test_order: None,
            testitem_type: relation.testitem_type.clone(),
            test_num: relation.test_num,
            test_txt: relation.test_txt.clone(),
            bin_relation: Some(bin_relation_json),
            hbins: Some(hbins),
            sbins: Some(sbins),
            unit_scale: None,
            custom_unit: Some(EMPTY.to_string()),
            test_order_manual_import_flag: Some(0),
            bin_relation_manual_import_flag: Some(0),
            create_time: Some(now),
            update_time: Some(now),
            create_user: Some("SYSTEM".to_string()),
            update_user: Some("SYSTEM".to_string()),
        }
    }

    /// Update existing DwTestProgramTestPlan with new bin relation data
    pub fn update_test_program_test_plan(
        &self,
        existing: &mut DwTestProgramTestPlan,
        new_relation: &TestProgramTestItemBinRelation,
    ) {
        let now = chrono::Utc::now().naive_utc();
        
        // Update fab and fab_site if not blank in new relation
        if let Some(ref fab) = new_relation.fab {
            if !fab.trim().is_empty() {
                existing.fab = Some(fab.clone());
            }
        }
        if let Some(ref fab_site) = new_relation.fab_site {
            if !fab_site.trim().is_empty() {
                existing.fab_site = Some(fab_site.clone());
            }
        }
        
        // Merge bin relations
        let existing_relations = self.parse_exists_bin_relation(existing.bin_relation.as_ref());
        let new_relations = new_relation.bin_relation.clone().unwrap_or_default();
        let merged_relations = self.merge_bin_relations(existing_relations, new_relations);
        
        // Update bin relation fields
        let bin_relation_json = serde_json::to_string(&merged_relations).unwrap_or_else(|_| EMPTY_JSON_ARRAY.to_string());
        existing.bin_relation = Some(bin_relation_json);
        existing.hbins = Some(self.sort_hbin_and_merge(&merged_relations));
        existing.sbins = Some(self.sort_sbin_and_merge(&merged_relations));
        existing.update_user = Some("SYSTEM".to_string());
        existing.update_time = Some(now);
    }

    // Helper functions for date formatting
    fn get_day_hour(&self, timestamp: i64) -> String {
        let dt = DateTime::from_timestamp_millis(timestamp).unwrap_or_else(|| Utc::now());
        dt.format("%Y%m%d%H").to_string()
    }

    fn get_day(&self, timestamp: i64) -> String {
        let dt = DateTime::from_timestamp_millis(timestamp).unwrap_or_else(|| Utc::now());
        dt.format("%Y%m%d").to_string()
    }
}