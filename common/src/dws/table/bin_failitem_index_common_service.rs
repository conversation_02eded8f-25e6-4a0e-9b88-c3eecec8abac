use crate::dto::dwd::die_detail_parquet::DieDetailParquet;
use crate::dto::dwd::file_detail::FileDetail;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::dto::dws::bin_failitem_index::BinFailitemIndex;
use crate::dto::dws::item_order::ItemOrder;
use crate::dto::dws::sub_bin_failitem::SubBinFailitem;
use std::collections::HashMap;
use chrono::Utc;

const MIDDLE_LINE: &str = "-";
const COMMA: &str = ",";
const EMPTY: &str = "";
const SYSTEM: &str = "SYSTEM";

#[derive(Debug, Clone)]
pub struct BinFailitemIndexCommonService;

impl BinFailitemIndexCommonService {
    pub fn new() -> Self {
        Self
    }

    /// Generate file to min pass die map from die details
    /// Corresponds to generateFileToMinPassDieMap in Scala
    pub fn generate_file_to_min_pass_die_map(
        die_detail: &Vec<DieDetailParquet>,
    ) -> Option<(i64, i64)> {
        if die_detail.is_empty() {
            return None;
        }

        let min_die = die_detail
            .iter()
            .min_by_key(|d| {
                (
                    d.HBIN_NUM.unwrap_or(i64::MAX),
                    d.SBIN_NUM.unwrap_or(i64::MAX),
                    d.C_PART_ID.unwrap_or(i64::MAX),
                )
            })?;

        Some((
            min_die.FILE_ID.unwrap_or(0),
            min_die.C_PART_ID.unwrap_or(0),
        ))
    }

    /// Generate file to item order map from test item details
    /// Corresponds to generateFileToItemOrderMap in Scala
    pub fn generate_file_to_item_order_map(
        file_id: i64,
        test_item_detail: &Vec<SubTestItemDetail>,
    ) -> (i64, Vec<ItemOrder>) {
        let mut item_orders: HashMap<String, ItemOrder> = HashMap::new();

        // Convert SubTestItemDetail to ItemOrder and group by TEST_ITEM
        for detail in test_item_detail {
            if let Some(test_item) = &detail.TEST_ITEM {
                let item_order = ItemOrder {
                    TEST_NUM: detail.TEST_NUM,
                    TEST_TXT: detail.TEST_TXT.clone(),
                    TESTITEM_TYPE: detail.TESTITEM_TYPE.clone(),
                    TEST_ITEM: detail.TEST_ITEM.clone(),
                    TEST_ORDER: detail.TEST_ORDER,
                    UNITS: detail.UNITS.clone(),
                    ORIGIN_UNITS: detail.ORIGIN_UNITS.clone(),
                };

                // Keep the one with minimum TEST_ORDER for each TEST_ITEM
                item_orders
                    .entry(test_item.clone())
                    .and_modify(|existing| {
                        if item_order.TEST_ORDER.unwrap_or(i64::MAX)
                            < existing.TEST_ORDER.unwrap_or(i64::MAX)
                        {
                            *existing = item_order.clone();
                        }
                    })
                    .or_insert(item_order);
            }
        }

        // Sort by TEST_ORDER
        let mut result: Vec<ItemOrder> = item_orders.into_values().collect();
        result.sort_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MAX));

        (file_id, result)
    }

    /// Calculate SubBinFailitem index from test item details and item orders
    /// Corresponds to calculateSubBinFailitemIndex in Scala
    pub fn calculate_sub_bin_failitem_index(
        test_item_detail: &Vec<SubTestItemDetail>,
        item_orders: &Vec<ItemOrder>,
        file_detail_map: &HashMap<u64, FileDetail>
    ) -> Option<SubBinFailitem> {
        if test_item_detail.is_empty() {
            return None;
        }

        // Get head from file_detail_map using file_id instead of test_item_detail[0]
        let file_id = test_item_detail[0].FILE_ID?;
        let head = file_detail_map.get(&(file_id as u64))?;
        let test_item_head = &test_item_detail[0]; // Keep reference for fields not in FileDetail
        let mut fail_item_orders: Vec<ItemOrder> = Vec::new();
        let mut fail_item_order_valid_flag = 0;

        if !item_orders.is_empty() {
            // Get failed test items (TEST_RESULT != 1)
            let fail_items: std::collections::HashSet<String> = test_item_detail
                .iter()
                .filter(|t| {
                    t.TEST_RESULT.is_some()
                        && t.TEST_RESULT != Some(1)
                        && t.TEST_ITEM.is_some()
                })
                .filter_map(|t| t.TEST_ITEM.clone())
                .collect();

            fail_item_orders = item_orders
                .iter()
                .filter(|item| {
                    item.TEST_ITEM
                        .as_ref()
                        .map_or(false, |test_item| fail_items.contains(test_item))
                })
                .cloned()
                .collect();
            fail_item_order_valid_flag = 1;
        } else {
            // Create ItemOrders from failed test items directly
            let mut item_map: HashMap<String, ItemOrder> = HashMap::new();
            for detail in test_item_detail {
                if detail.TEST_RESULT.is_some()
                    && detail.TEST_RESULT != Some(1)
                    && detail.TEST_ITEM.is_some()
                {
                    let test_item = detail.TEST_ITEM.as_ref().unwrap();
                    let item_order = ItemOrder {
                        TEST_NUM: detail.TEST_NUM,
                        TEST_TXT: detail.TEST_TXT.clone(),
                        TESTITEM_TYPE: detail.TESTITEM_TYPE.clone(),
                        TEST_ITEM: detail.TEST_ITEM.clone(),
                        TEST_ORDER: detail.TEST_ORDER,
                        UNITS: detail.UNITS.clone(),
                        ORIGIN_UNITS: detail.ORIGIN_UNITS.clone(),
                    };

                    item_map
                        .entry(test_item.clone())
                        .and_modify(|existing| {
                            if item_order.TEST_ORDER.unwrap_or(i64::MAX)
                                < existing.TEST_ORDER.unwrap_or(i64::MAX)
                            {
                                *existing = item_order.clone();
                            }
                        })
                        .or_insert(item_order);
                }
            }
            fail_item_orders = item_map.into_values().collect();
            fail_item_orders.sort_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MAX));
        }

        let first_fail_item = if !fail_item_orders.is_empty() {
            Some(fail_item_orders[0].clone())
        } else {
            None
        };

        let last_fail_item = if !fail_item_orders.is_empty() {
            Some(fail_item_orders[fail_item_orders.len() - 1].clone())
        } else {
            None
        };

        Some(SubBinFailitem {
            CUSTOMER: Some(head.CUSTOMER.clone()),
            SUB_CUSTOMER: Some(head.SUB_CUSTOMER.clone()),
            UPLOAD_TYPE: Some(head.UPLOAD_TYPE.clone()),
            FACTORY: Some(head.FACTORY.clone()),
            FACTORY_SITE: Some(head.FACTORY_SITE.clone()),
            FAB: Some(head.FAB.clone()),
            FAB_SITE: Some(head.FAB_SITE.clone()),
            TEST_AREA: Some(head.TEST_AREA.clone()),
            TEST_STAGE: Some(head.TEST_STAGE.clone()),
            LOT_TYPE: Some(head.LOT_TYPE.clone()),
            DEVICE_ID: Some(head.DEVICE_ID.clone()),
            LOT_ID: Some(head.LOT_ID.clone()),
            SBLOT_ID: Some(head.SBLOT_ID.clone()),
            WAFER_LOT_ID: test_item_head.WAFER_LOT_ID.clone(),
            WAFER_ID: test_item_head.WAFER_ID.clone(),
            WAFER_NO: test_item_head.WAFER_NO.clone(),
            TEST_PROGRAM: Some(head.TEST_PROGRAM.clone()),
            TEST_TEMPERATURE: Some(head.TEST_TEMPERATURE.clone()),
            TEST_PROGRAM_VERSION: Some(head.TEST_PROGRAM_VERSION.clone()),
            FILE_ID: Some(head.FILE_ID),
            FILE_NAME: Some(head.FILE_NAME.clone()),
            FILE_TYPE: Some(head.FILE_TYPE.clone()),
            TESTER_NAME: Some(head.TESTER_NAME.clone()),
            TESTER_TYPE: Some(head.TESTER_TYPE.clone()),
            PROBER_HANDLER_ID: Some(head.PROBER_HANDLER_ID.clone()),
            PROBECARD_LOADBOARD_ID: Some(head.PROBECARD_LOADBOARD_ID.clone()),
            START_TIME: head.START_TIME.map(|t| t.timestamp_millis()),
            END_TIME: head.END_TIME.map(|t| t.timestamp_millis()),
            START_HOUR_KEY: Some(head.START_HOUR_KEY.clone()),
            START_DAY_KEY: Some(head.START_DAY_KEY.clone()),
            END_HOUR_KEY: Some(head.END_HOUR_KEY.clone()),
            END_DAY_KEY: Some(head.END_DAY_KEY.clone()),
            FLOW_ID: Some(head.FLOW_ID.clone()),
            ONLINE_RETEST: test_item_head.ONLINE_RETEST,
            ECID: test_item_head.ECID.clone(),
            HBIN_NUM: test_item_head.HBIN_NUM,
            HBIN_NAM: test_item_head.HBIN_NAM.clone(),
            HBIN_PF: test_item_head.HBIN_PF.clone(),
            HBIN: Self::string_value(test_item_head.HBIN_NUM),
            SBIN_NUM: test_item_head.SBIN_NUM,
            SBIN_NAM: test_item_head.SBIN_NAM.clone(),
            SBIN_PF: test_item_head.SBIN_PF.clone(),
            SBIN: Self::string_value(test_item_head.SBIN_NUM),
            FIRST_FAIL_ITEM: first_fail_item,
            LAST_FAIL_ITEM: last_fail_item,
            ALL_FAIL_ITEM: if fail_item_orders.is_empty() {
                None
            } else {
                Some(fail_item_orders)
            },
            ORDER_VALID_FLAG: Some(fail_item_order_valid_flag),
            PROCESS: head.PROCESS.clone(),
            UPLOAD_TIME: head.UPLOAD_TIME.map(|t| t.timestamp_millis()),
        })
    }

    /// Generate CP final flag from SubBinFailitem records
    /// Corresponds to generateCpFinalFlag in Scala
    pub fn generate_cp_final_flag(
        records: &mut Vec<SubBinFailitem>,
    ) -> Vec<(SubBinFailitem, i32)> {
        if records.is_empty() {
            return Vec::new();
        }

        // Sort by ONLINE_RETEST
        records.sort_by_key(|r| r.ONLINE_RETEST.unwrap_or(0));

        let mut result = Vec::new();
        if !records.is_empty() {
            // First record gets finalFlag=0, last gets finalFlag=1
            result.push((records[0].clone(), 0));
            if records.len() > 1 {
                result.push((records[records.len() - 1].clone(), 1));
            }
        }

        result
    }

    /// Convert ItemOrder to item detail string
    /// Corresponds to toItemDetail(itemOrder: ItemOrder) in Scala
    pub fn to_item_detail(item_order: &ItemOrder) -> String {
        format!(
            "{}{}{}",
            item_order.TESTITEM_TYPE.as_deref().unwrap_or(""),
            MIDDLE_LINE,
            item_order.TEST_ITEM.as_deref().unwrap_or("")
        )
    }

    /// Convert ItemOrder list to item detail string list
    /// Corresponds to toItemDetail(itemOrders: List[ItemOrder]) in Scala
    pub fn to_item_detail_list(item_orders: &Vec<ItemOrder>) -> Vec<String> {
        item_orders.iter().map(Self::to_item_detail).collect()
    }

    /// Calculate BinFailitemIndex from SubBinFailitem records
    /// Corresponds to calculateBinFailitemIndex in Scala
    pub fn calculate_bin_failitem_index(
        sub_bin_failitems: &Vec<SubBinFailitem>,
        final_flag: i32,
    ) -> Option<BinFailitemIndex> {
        if sub_bin_failitems.is_empty() {
            return None;
        }

        let head = &sub_bin_failitems[0];

        // Collect first fail items (distinct)
        let mut first_fail_items: Vec<ItemOrder> = Vec::new();
        let mut seen_first: std::collections::HashSet<String> = std::collections::HashSet::new();

        // Collect all fail items (distinct)
        let mut all_fail_items: Vec<ItemOrder> = Vec::new();
        let mut seen_all: std::collections::HashSet<String> = std::collections::HashSet::new();

        // Collect last fail items
        let mut last_fail_items: Vec<ItemOrder> = Vec::new();

        for sub_bin in sub_bin_failitems {
            if let Some(ref all_fail_item) = sub_bin.ALL_FAIL_ITEM {
                if !all_fail_item.is_empty() {
                    // First fail item
                    if let Some(ref first_fail) = sub_bin.FIRST_FAIL_ITEM {
                        if let Some(ref test_item) = first_fail.TEST_ITEM {
                            if !seen_first.contains(test_item) {
                                first_fail_items.push(first_fail.clone());
                                seen_first.insert(test_item.clone());
                            }
                        }
                    }

                    // All fail items
                    for item in all_fail_item {
                        if let Some(ref test_item) = item.TEST_ITEM {
                            if !seen_all.contains(test_item) {
                                all_fail_items.push(item.clone());
                                seen_all.insert(test_item.clone());
                            }
                        }
                    }

                    // Last fail item
                    if let Some(ref last_fail) = sub_bin.LAST_FAIL_ITEM {
                        last_fail_items.push(last_fail.clone());
                    }
                }
            }
        }

        let first_failitem_cnt = first_fail_items.len() as i64;
        let all_failitem_cnt = all_fail_items.len() as i64;

        let mut first_failitem_detail = EMPTY.to_string();
        let mut last_failitem_of_all = EMPTY.to_string();
        let mut all_failitem_detail = EMPTY.to_string();

        if head.ORDER_VALID_FLAG == Some(1) && !last_fail_items.is_empty() {
            // Sort first fail items by TEST_ORDER
            first_fail_items.sort_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MAX));
            first_failitem_detail = first_fail_items
                .iter()
                .map(Self::to_item_detail)
                .collect::<Vec<_>>()
                .join(COMMA);

            // Get last fail item with max TEST_ORDER
            if let Some(last_item) = last_fail_items
                .iter()
                .max_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MIN))
            {
                last_failitem_of_all = Self::to_item_detail(last_item);
            }

            // Sort all fail items by TEST_ORDER and take first 100
            all_fail_items.sort_by_key(|item| item.TEST_ORDER.unwrap_or(i64::MAX));
            all_failitem_detail = all_fail_items
                .iter()
                .take(100)
                .map(Self::to_item_detail)
                .collect::<Vec<_>>()
                .join(COMMA);
        }

        let now = Utc::now().timestamp_millis();
        let create_hour_key = Self::get_day_hour(now);
        let create_day_key = Self::get_day(now);

        Some(BinFailitemIndex {
            CUSTOMER: head.CUSTOMER.clone(),
            SUB_CUSTOMER: head.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: head.UPLOAD_TYPE.clone(),
            FACTORY: head.FACTORY.clone(),
            FACTORY_SITE: head.FACTORY_SITE.clone(),
            FAB: head.FAB.clone(),
            FAB_SITE: head.FAB_SITE.clone(),
            TEST_AREA: head.TEST_AREA.clone(),
            TEST_STAGE: head.TEST_STAGE.clone(),
            LOT_TYPE: head.LOT_TYPE.clone(),
            DEVICE_ID: head.DEVICE_ID.clone(),
            LOT_ID: head.LOT_ID.clone(),
            SBLOT_ID: head.SBLOT_ID.clone(),
            WAFER_LOT_ID: head.WAFER_LOT_ID.clone(),
            WAFER_ID: head.WAFER_ID.clone(),
            WAFER_ID_KEY: head.WAFER_ID.clone(),
            WAFER_NO: head.WAFER_NO.clone(),
            WAFER_NO_KEY: head.WAFER_NO.clone(),
            TEST_PROGRAM: head.TEST_PROGRAM.clone(),
            TEST_TEMPERATURE: head.TEST_TEMPERATURE.clone(),
            TEST_PROGRAM_VERSION: head.TEST_PROGRAM_VERSION.clone(),
            FILE_ID: head.FILE_ID,
            FILE_NAME: head.FILE_NAME.clone(),
            FILE_TYPE: head.FILE_TYPE.clone(),
            TESTER_NAME: head.TESTER_NAME.clone(),
            TESTER_TYPE: head.TESTER_TYPE.clone(),
            PROBER_HANDLER_ID: head.PROBER_HANDLER_ID.clone(),
            PROBECARD_LOADBOARD_ID: head.PROBECARD_LOADBOARD_ID.clone(),
            START_TIME: head.START_TIME,
            END_TIME: head.END_TIME,
            START_HOUR_KEY: head.START_HOUR_KEY.clone(),
            START_DAY_KEY: head.START_DAY_KEY.clone(),
            END_HOUR_KEY: head.END_HOUR_KEY.clone(),
            END_DAY_KEY: head.END_DAY_KEY.clone(),
            FLOW_ID: head.FLOW_ID.clone(),
            FINAL_FLAG: Some(final_flag),
            HBIN_NUM: head.HBIN_NUM,
            HBIN_NUM_KEY: Self::string_value(head.HBIN_NUM),
            HBIN_NAM: head.HBIN_NAM.clone(),
            HBIN_PF: head.HBIN_PF.clone(),
            HBIN: head.HBIN.clone(),
            SBIN_NUM: head.SBIN_NUM,
            SBIN_NUM_KEY: Self::string_value(head.SBIN_NUM),
            SBIN_NAM: head.SBIN_NAM.clone(),
            SBIN_PF: head.SBIN_PF.clone(),
            SBIN: head.SBIN.clone(),
            FIRST_FAIL_ITEM_CNT: Some(first_failitem_cnt),
            FIRST_FAIL_ITEM_DETAIL: Some(first_failitem_detail),
            LAST_FAIL_ITEM_OF_ALL: Some(last_failitem_of_all),
            ALL_FAIL_ITEM_CNT: Some(all_failitem_cnt),
            ALL_FAIL_ITEM_DETAIL: Some(all_failitem_detail),
            CREATE_HOUR_KEY: Some(create_hour_key),
            CREATE_DAY_KEY: Some(create_day_key),
            CREATE_TIME: Some(now),
            CREATE_USER: Some(SYSTEM.to_string()),
            VERSION: Some(now),
            PROCESS: head.PROCESS.clone(),
            UPLOAD_TIME: head.UPLOAD_TIME,
        })
    }

    /// Helper function to convert Option<i64> to Option<String>
    fn string_value(value: Option<i64>) -> Option<String> {
        value.map(|v| v.to_string())
    }

    /// Helper function to get day hour key from timestamp
    fn get_day_hour(timestamp: i64) -> String {
        let dt = chrono::DateTime::from_timestamp_millis(timestamp)
            .unwrap_or_else(|| Utc::now());
        dt.format("%Y%m%d%H").to_string()
    }

    /// Helper function to get day key from timestamp
    fn get_day(timestamp: i64) -> String {
        let dt = chrono::DateTime::from_timestamp_millis(timestamp)
            .unwrap_or_else(|| Utc::now());
        dt.format("%Y%m%d").to_string()
    }
}