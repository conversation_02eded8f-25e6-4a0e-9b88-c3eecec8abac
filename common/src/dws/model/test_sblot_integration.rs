#[cfg(test)]
mod tests {
    use crate::dws::model::sblot_aggregation::*;
    use std::collections::HashMap;

    #[test]
    fn test_sblot_aggregation_integration() {
        // 创建测试数据
        let mut sblot_map = HashMap::new();

        let sblot1 = Sblot {
            customer: "customer1".to_string(),
            factory: "factory1".to_string(),
            device_id: "device1".to_string(),
            test_area: "FT".to_string(),
            lot_id: "lot1".to_string(),
            lot_type: "type1".to_string(),
            sblot_id: "sblot1".to_string(),
            test_stage: "stage1".to_string(),
            test_program: "program1".to_string(),
        };

        let mut bin_map1 = HashMap::new();
        bin_map1.insert("bin1".to_string(), 10);
        bin_map1.insert("bin2".to_string(), 20);
        sblot_map.insert(sblot1.clone(), bin_map1);

        // 同一个lot的另一个sblot
        let sblot2 = Sblot {
            customer: "customer1".to_string(),
            factory: "factory1".to_string(),
            device_id: "device1".to_string(),
            test_area: "FT".to_string(),
            lot_id: "lot1".to_string(),
            lot_type: "type1".to_string(),
            sblot_id: "sblot2".to_string(),
            test_stage: "stage1".to_string(),
            test_program: "program1".to_string(),
        };

        let mut bin_map2 = HashMap::new();
        bin_map2.insert("bin2".to_string(), 15); // 应该取max(20, 15) = 20
        bin_map2.insert("bin3".to_string(), 30);
        sblot_map.insert(sblot2, bin_map2);

        // 执行聚合
        let lot_map = aggregate_sblot_to_lot(sblot_map);

        // 验证结果
        assert_eq!(lot_map.len(), 1);
        let lot = Lot::from(&sblot1);
        let bins = lot_map.get(&lot).expect("Lot should exist in result");

        assert_eq!(*bins.get(&"bin1".to_string()).expect("bin1 should exist"), 10);
        assert_eq!(*bins.get(&"bin2".to_string()).expect("bin2 should exist"), 20); // 取最大值
        assert_eq!(*bins.get(&"bin3".to_string()).expect("bin3 should exist"), 30);
    }
}