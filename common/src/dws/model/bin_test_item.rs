use crate::parquet::RecordBatchWrapper;
use arrow::datatypes::FieldRef;
use arrow::record_batch::RecordBatch;
use serde::{Deserialize, Serialize};
use serde_arrow::schema::SchemaLike;
use serde_arrow::schema::TracingOptions;

/// Bin测试项索引数据结构
/// 对应Scala中的BinTestItemIndex case class
#[derive(Debug, Clone, Serialize, Deserialize, clickhouse::Row)]
pub struct BinTestItemIndex {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: i64,
    pub FILE_NAME: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub LOT_TYPE: String,
    pub DEVICE_ID: String,
    pub LOT_ID: String,
    pub WAFER_ID: String,
    pub WAFER_ID_KEY: String,
    pub WAFER_NO: String,
    pub WAFER_NO_KEY: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TESTER_NAME: String,
    pub TESTER_TYPE: String,
    pub PROBER_HANDLER_ID: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub HBIN_NUM: i64,
    pub HBIN_NUM_KEY: String,
    pub SBIN_NUM: i64,
    pub SBIN_NUM_KEY: String,
    pub SBIN_PF: String,
    pub SBIN_NAM: String,
    pub HBIN_PF: String,
    pub HBIN_NAM: String,
    pub HBIN: String,
    pub SBIN: String,
    pub TESTITEM_TYPE: String,
    pub TEST_NUM: i64,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub HI_SPEC: f64,
    pub LO_SPEC: f64,
    pub HI_LIMIT: f64,
    pub LO_LIMIT: f64,
    pub FIRST_PASS_CNT: i64,
    pub FINAL_PASS_CNT: i64,
    pub FIRST_FAIL_CNT: i64,
    pub FINAL_FAIL_CNT: i64,
    pub TOTAL_CNT: i64,
    pub FIRST_MEAN: Option<f64>,
    pub FINAL_MEAN: Option<f64>,
    pub FIRST_SUM: Option<f64>,
    pub FINAL_SUM: Option<f64>,
    pub FIRST_STANDARD_SQUARE_SUM: Option<f64>,
    pub FINAL_STANDARD_SQUARE_SUM: Option<f64>,
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    pub CREATE_TIME: i64,
    pub CREATE_USER: String,
    pub VERSION: i64,
    pub PROCESS: String,
    pub UPLOAD_TIME: i64,
}

impl RecordBatchWrapper for BinTestItemIndex {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<BinTestItemIndex> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }
        let fields = Vec::<FieldRef>::from_type::<BinTestItemIndex>(
            TracingOptions::default()
        )
        .map_err(|e| format!("Failed to create schema from samples: {}", e))?;
        let record_batch = serde_arrow::to_record_batch(&fields, &data)
            .map_err(|e| format!("Failed to serialize to RecordBatch: {}", e))?;
        Ok(record_batch)
    }
}

/// Bin测试项索引子结构
/// 对应Scala中的SubBinTestItemIndex case class
#[derive(Debug, Clone)]
pub struct SubBinTestItemIndex {
    pub tester_names: String,
    pub tester_types: String,
    pub prober_handler_ids: String,
    pub prober_card_load_board_ids: String,
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub start_hour_key: String,
    pub start_day_key: String,
    pub end_hour_key: String,
    pub end_day_key: String,
    pub first_pass_cnt: i64,
    pub final_pass_cnt: i64,
    pub first_fail_cnt: i64,
    pub final_fail_cnt: i64,
    pub total_cnt: i64,
    pub first_mean: Option<f64>,
    pub final_mean: Option<f64>,
    pub first_sum: Option<f64>,
    pub final_sum: Option<f64>,
    pub first_standard_square_sum: Option<f64>,
    pub final_standard_square_sum: Option<f64>,
}