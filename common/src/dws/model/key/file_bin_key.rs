
/// Key structure for grouping by file and bin information
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub struct FileBinKey {
    pub file_id: Option<i64>,
    pub hbin_num: Option<i64>,
    pub hbin_nam: Option<String>,
    pub hbin_pf: Option<String>,
    pub sbin_num: Option<i64>,
    pub sbin_nam: Option<String>,
    pub sbin_pf: Option<String>,
    pub final_flag: i32,
}

impl FileBinKey {
    pub fn new(
        file_id: Option<i64>,
        hbin_num: Option<i64>,
        hbin_nam: Option<String>,
        hbin_pf: Option<String>,
        sbin_num: Option<i64>,
        sbin_nam: Option<String>,
        sbin_pf: Option<String>,
        final_flag: i32,
    ) -> Self {
        Self {
            file_id,
            hbin_num,
            hbin_nam,
            hbin_pf,
            sbin_num,
            sbin_nam,
            sbin_pf,
            final_flag,
        }
    }
}