#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub struct ProgramTestItemBin {
    pub customer: String,
    pub test_area: String,
    pub factory: String,
    pub test_stage: String,
    pub device_id: String,
    pub lot_type: String,
    pub test_program: String,
    pub test_program_version: String,
    pub test_item: String,
    pub hbin_num: Option<i64>,
    pub hbin_name: String,
    pub hbin_pf: String,
    pub sbin_num: Option<i64>,
    pub sbin_name: String,
    pub sbin_pf: String,
}