use crate::parquet::RecordBatchWrapper;
use arrow::datatypes::FieldRef;
use arrow::record_batch::RecordBatch;
use serde::{Deserialize, Serialize};
use serde_arrow::schema::SchemaLike;
use serde_arrow::schema::TracingOptions;

/// 测试项索引数据结构
/// 对应Scala中的TestItemIndex case class
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, clickhouse::Row)]
pub struct TestItemIndex {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub LOT_TYPE: String,
    pub DEVICE_ID: String,
    pub LOT_ID: String,
    pub SBLOT_ID: String,
    pub WAFER_LOT_ID: String,
    pub WAFER_ID: String,
    pub WAFER_ID_KEY: String,
    pub WAFER_NO: String,
    pub WAFER_NO_KEY: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub OFFLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub LO_LIMIT: f64,
    pub HI_LIMIT: f64,
    pub ORIGIN_HI_LIMIT: f64,
    pub ORIGIN_LO_LIMIT: f64,
    pub FILE_ID: i64,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub TESTER_NAME: String,
    pub TESTER_TYPE: String,
    pub PROBER_HANDLER_ID: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub START_TIME: i64,
    pub END_TIME: i64,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub FLOW_ID: String,
    pub FINAL_FLAG: i32,
    pub BIN_PF: String,
    pub TESTITEM_TYPE: String,
    pub TEST_NUM: i64,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub UNITS: String,
    pub ORIGIN_UNITS: String,
    pub MEDIAN: f64,
    pub MEAN: f64,
    pub MAX: f64,
    pub MIN: f64,
    pub STANDARD_DEVIATION: f64,
    pub RANGE: f64,
    pub IQR: f64,
    pub Q1: f64,
    pub Q3: f64,
    pub CNT: i64,
    pub FAIL_CNT: i64,
    pub FAIL_RATE: f64,
    pub CP: f64,
    pub CPK: f64,
    pub RETEST_BIN_NUM: String,
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    pub CREATE_TIME: i64,
    pub CREATE_USER: String,
    pub VERSION: i64,
    pub PROCESS: String,
    pub UPLOAD_TIME: i64,
}

impl RecordBatchWrapper for TestItemIndex {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<TestItemIndex> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }
        let fields = Vec::<FieldRef>::from_type::<TestItemIndex>(
            TracingOptions::default()
        )
        .map_err(|e| format!("Failed to create schema from samples: {}", e))?;
        let record_batch = serde_arrow::to_record_batch(&fields, &data)
            .map_err(|e| format!("Failed to serialize to RecordBatch: {}", e))?;
        Ok(record_batch)
    }
}