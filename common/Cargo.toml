[package]
name = "common"
version = "0.1.0"
edition = "2021"

[dependencies]
mysql-provider = { path = '../mysql-provider' }
ck-provider = { path = '../ck-provider' }
serde = { workspace = true }
rust_decimal = { workspace = true }
clickhouse = { workspace = true }
chrono = { workspace = true }
fixnum = { workspace = true }
average = "0.15.1"
arrow = { workspace = true }
serde_arrow = { workspace = true }
log = { workspace = true }
sqlx = { workspace = true }
tokio = { workspace = true }
lazy_static = "1.4.0"
anyhow =  { workspace = true }
url = "2.5.4"
tracing = { workspace = true }
serde_json = { workspace = true }