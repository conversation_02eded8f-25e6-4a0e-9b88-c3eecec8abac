  1. FILE_ID                                  int64
  2. ONLINE_RETEST                            int32
  3. MAX_OFFLINE_RETEST                       int32
  4. MAX_ONLINE_RETEST                        int32
  5. IS_DIE_FIRST_TEST                        int32
  6. IS_DIE_FINAL_TEST                        int32
  7. IS_FIRST_TEST                            int32
  8. IS_FINAL_TEST                            int32
  9. IS_FIRST_TEST_IGNORE_TP                  int32
 10. IS_FINAL_TEST_IGNORE_TP                  int32
 11. IS_DUP_FIRST_TEST                        int32
 12. IS_DUP_FINAL_TEST                        int32
 13. IS_DUP_FIRST_TEST_IGNORE_TP              int32
 14. IS_DUP_FINAL_TEST_IGNORE_TP              int32
 15. TEST_SUITE                               string
 16. CONDITION_SET                            map<string, string ('CONDITION_SET')>
 17. TEST_NUM                                 int64
 18. TEST_TXT                                 string
 19. TEST_ITEM                                string
 20. IS_DIE_FIRST_TEST_ITEM                   int32
 21. TESTITEM_TYPE                            string
 22. TEST_FLG                                 string
 23. PARM_FLG                                 string
 24. TEST_STATE                               string
 25. TEST_VALUE                               decimal128(38, 18)
 26. UNITS                                    string
 27. TEST_RESULT                              int32
 28. ORIGIN_TEST_VALUE                        decimal128(38, 18)
 29. ORIGIN_UNITS                             string
 30. TEST_ORDER                               int64
 31. ALARM_ID                                 string
 32. OPT_FLG                                  string
 33. RES_SCAL                                 int32
 34. NUM_TEST                                 int32
 35. LLM_SCAL                                 int32
 36. HLM_SCAL                                 int32
 37. LO_LIMIT                                 decimal128(38, 18)
 38. HI_LIMIT                                 decimal128(38, 18)
 39. ORIGIN_HI_LIMIT                          decimal128(38, 18)
 40. ORIGIN_LO_LIMIT                          decimal128(38, 18)
 41. C_RESFMT                                 string
 42. C_LLMFMT                                 string
 43. C_HLMFMT                                 string
 44. LO_SPEC                                  decimal128(38, 18)
 45. HI_SPEC                                  decimal128(38, 18)
 46. HBIN_NUM                                 int64
 47. SBIN_NUM                                 int64
 48. SBIN_PF                                  string
 49. SBIN_NAM                                 string
 50. HBIN_PF                                  string
 51. HBIN_NAM                                 string
 52. HBIN                                     string
 53. SBIN                                     string
 54. TEST_HEAD                                int64
 55. PART_FLG                                 string
 56. PART_ID                                  string
 57. C_PART_ID                                int64
 58. ECID                                     string
 59. ECID_EXT                                 string
 60. ECID_EXTRA                               map<string, string ('ECID_EXTRA')>
 61. IS_STANDARD_ECID                         int32
 62. X_COORD                                  int32
 63. Y_COORD                                  int32
 64. DIE_X                                    int32
 65. DIE_Y                                    int32
 66. TEST_TIME                                int64
 67. PART_TXT                                 string
 68. PART_FIX                                 string
 69. SITE                                     int64
 70. TOUCH_DOWN_ID                            int32
 71. WAFER_LOT_ID                             string
 72. WAFER_ID                                 string
 73. WAFER_NO                                 string
 74. RETICLE_T_X                              int32
 75. RETICLE_T_Y                              int32
 76. RETICLE_X                                int32
 77. RETICLE_Y                                int32
 78. SITE_ID                                  string
 79. VECT_NAM                                 string
 80. TIME_SET                                 string
 81. NUM_FAIL                                 int64
 82. FAIL_PIN                                 string
 83. CYCL_CNT                                 int64
 84. REPT_CNT                                 int64
 85. LONG_ATTRIBUTE_SET                       map<string, int64 ('LONG_ATTRIBUTE_SET')>
 86. STRING_ATTRIBUTE_SET                     map<string, string ('STRING_ATTRIBUTE_SET')>
 87. FLOAT_ATTRIBUTE_SET                      map<string, decimal128(38, 18) ('FLOAT_ATTRIBUTE_SET')>
 88. UID                                      string
 89. TEXT_DAT                                 string
 90. CREATE_HOUR_KEY                          string
 91. CREATE_DAY_KEY                           string
 92. CREATE_TIME                              int64
 93. EFUSE_EXTRA                              map<string, string ('EFUSE_EXTRA')>
 94. CHIP_ID                                  string