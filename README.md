## Project Overview

This is a Rust data processing workspace for migrating Java/Scala data warehouse computations to Rust. It's designed to handle semiconductor test data (STDF format) and process it through various data warehouse layers (ODS, DWD, DIM, DWS, ADS).

## Build and Development Commands

### Build Commands
- `cargo build` - Build debug version
- `cargo build --release` - Build release version
- `bash build.sh` - Build using the provided script (equivalent to `cargo build --release`)

### Development Commands
- `cargo check` - Check for compilation errors without building
- `cargo check --workspace` - Check all workspace members
- `cargo test` - Run all tests
- `cargo test --package <package>` - Run tests for specific package
- `cargo fmt` - Format code using rustfmt
- `cargo clippy` - Run linter

### Testing Individual Components
- `cargo test --package ck-provider` - Test ClickHouse provider
- `cargo test --package mysql-provider` - Test MySQL provider
- `cargo test --package parquet-provider` - Test Parquet provider
- `cargo test --package dw-test-item` - Test TestItem business logic (21 unit tests)

## Project Architecture

### Workspace Structure
This is a Cargo workspace with the following crates:

**Library Crates (Providers & Utilities):**
- `common/` - Shared data models, DTOs, and utilities
- `ck-provider/` - ClickHouse database operations
- `mysql-provider/` - MySQL database operations
- `parquet-provider/` - Parquet file read/write operations
- `redis-provider/` - Redis operations and distributed locking
- `kafka-provider/` - Kafka message handling
- `utils/` - Common utility functions

**Application Crates:**
- `dw-test-item/` - Main application for test item data processing (**RECENTLY MIGRATED TO RUST**)
- `dw-die/` - Die-level data processing

### Data Processing Flow
The system processes semiconductor test data through these layers:
1. **ODS (Operational Data Store)** - Raw STDF file processing
2. **DWD (Data Warehouse Detail)** - Detailed fact tables
3. **DIM (Dimension)** - Dimension tables
4. **DWS (Data Warehouse Summary)** - Summary/aggregation tables
5. **ADS (Application Data Service)** - Application-specific data marts

### Key Components

#### Configuration Management
- Uses Java Properties files for configuration (`dw-test-item/src/config.rs`)
- Configuration loaded from `resources/properties/dataware-dw-test-item-3.4.2.properties`
- Supports multiple ClickHouse addresses with load balancing

#### Database Providers
- **ClickHouse**: Primary analytical database with batch operations and parallel writes
- **MySQL**: Metadata and operational data storage
- **Redis**: Distributed locking and caching
- **Parquet**: File-based data storage with compression

#### Data Models
- Located in `common/src/dto/` and `common/src/dwd/`
- Separated by data warehouse layer (ads, dwd, dws)
- Uses Serde for serialization/deserialization

## Development Notes

### Code Style
- Uses rustfmt with max_width = 120
- Automatic import reordering enabled
- Chain width limit of 80 characters

### Error Handling
- Uses `anyhow` for general error handling
- Provider-specific error types with `thiserror`
- Comprehensive logging with structured messages

### Async Operations
- Heavy use of `tokio` for async operations
- Parallel processing capabilities in providers
- Batch operations for database writes

### Testing
- Unit tests in each provider crate
- Integration tests using real database connections
- Examples provided in `examples/` directories

## Migration Context

This project is migrating from a Java/Scala Spark-based data processing system to Rust. The original architecture used:
- Java for configuration and utilities
- Scala for Spark-based data processing
- Multiple database backends (ClickHouse, MySQL, Redis)

The Rust version maintains the same data processing logic while providing better performance and memory safety.
- Use tokio instead of spark for calculations
- There is no bulkload implementation

**The most important note**: If the Scala and Rust implementations do not match, the Scala version takes precedence and the Rust version must be reimplemented to ensure fully consistent business logic.

