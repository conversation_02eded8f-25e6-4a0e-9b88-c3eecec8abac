use arrow::datatypes::FieldRef;
use arrow::record_batch::RecordBatch;
use parquet_provider::RecordBatchWrapper;
use serde::{Deserialize, Serialize};
use serde_arrow::schema::{SchemaLike, TracingOptions};
use std::collections::HashMap;

#[derive(Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct DieData {
    pub customer: Option<String>,
    pub sub_customer: Option<String>,
    pub file_id: Option<i64>,
    pub file_name: Option<String>,
    pub device_id: Option<String>,
    pub factory: Option<String>,
    pub factory_site: Option<String>,
    pub lot_type: Option<String>,
    pub lot_id: Option<String>,
    pub sblot_id: Option<String>,
    pub test_area: Option<String>,
    pub test_stage: Option<String>,
    pub offline_retest: Option<i32>,
    pub offline_retest_ignore_tp: Option<i32>,
    pub online_retest: Option<i32>,
    pub interrupt: Option<i32>,
    pub interrupt_ignore_tp: Option<i32>,
    pub dup_retest: Option<i32>,
    pub dup_retest_ignore_tp: Option<i32>,
    pub batch_num: Option<i32>,
    pub batch_num_ignore_tp: Option<i32>,
    pub num_test: Option<i32>,
    pub test_program: Option<String>,
    pub test_temperature: Option<String>,
    pub test_program_version: Option<String>,
    pub spec_nam: Option<String>,
    pub spec_ver: Option<String>,
    pub hbin_num: Option<i64>,
    pub sbin_num: Option<i64>,
    pub sbin_pf: Option<String>,
    pub sbin_nam: Option<String>,
    pub hbin_pf: Option<String>,
    pub hbin_nam: Option<String>,
    pub test_head: Option<i64>,
    pub tester_name: Option<String>,
    pub tester_type: Option<String>,
    pub operator_name: Option<String>,
    pub prober_handler_typ: Option<String>,
    pub prober_handler_id: Option<String>,
    pub probecard_loadboard_typ: Option<String>,
    pub probecard_loadboard_id: Option<String>,
    pub part_flg: Option<String>,
    pub part_id: Option<String>,
    pub c_part_id: Option<i32>,
    pub x_coord: Option<i32>,
    pub y_coord: Option<i32>,
    pub test_time: Option<i64>,
    pub part_txt: Option<String>,
    pub part_fix: Option<String>,
    pub site: Option<i64>,
    pub site_grp: Option<i64>,
    pub site_cnt: Option<i64>,
    pub site_nums: Option<String>,
    pub start_time: Option<i64>,
    pub end_time: Option<i64>,
    pub wafer_no: Option<String>,
    pub wafer_id: Option<String>,
    pub wafer_size: Option<f64>,
    pub die_height: Option<f64>,
    pub die_width: Option<f64>,
    pub wf_units: Option<i64>,
    pub wf_flat: Option<String>,
    pub test_cod: Option<String>,
    pub center_x: Option<i32>,
    pub center_y: Option<i32>,
    pub pos_x: Option<String>,
    pub pos_y: Option<String>,
    pub die_cnt: Option<i64>,
    pub part_cnt: Option<i64>,
    pub rtst_cnt: Option<i64>,
    pub abrt_cnt: Option<i64>,
    pub good_cnt: Option<i64>,
    pub func_cnt: Option<i64>,
    pub fabwf_id: Option<String>,
    pub frame_id: Option<String>,
    pub mask_id: Option<String>,
    pub wafer_usr_desc: Option<String>,
    pub wafer_exc_desc: Option<String>,
    pub setup_t: Option<i64>,
    pub stat_num: Option<i64>,
    pub mode_cod: Option<String>,
    pub prot_cod: Option<String>,
    pub burn_tim: Option<i64>,
    pub cmod_cod: Option<String>,
    pub exec_typ: Option<String>,
    pub exec_ver: Option<String>,
    pub user_txt: Option<String>,
    pub aux_file: Option<String>,
    pub pkg_typ: Option<String>,
    pub famly_id: Option<String>,
    pub date_cod: Option<String>,
    pub facil_id: Option<String>,
    pub floor_id: Option<String>,
    pub proc_id: Option<String>,
    pub oper_frq: Option<String>,
    pub flow_id: Option<String>,
    pub flow_id_ignore_tp: Option<String>,
    pub setup_id: Option<String>,
    pub dsgn_rev: Option<String>,
    pub eng_id: Option<String>,
    pub rom_cod: Option<String>,
    pub serl_num: Option<String>,
    pub supr_nam: Option<String>,
    pub disp_cod: Option<String>,
    pub lot_usr_desc: Option<String>,
    pub lot_exc_desc: Option<String>,
    pub dib_typ: Option<String>,
    pub dib_id: Option<String>,
    pub cabl_typ: Option<String>,
    pub cabl_id: Option<String>,
    pub cont_typ: Option<String>,
    pub cont_id: Option<String>,
    pub lasr_typ: Option<String>,
    pub lasr_id: Option<String>,
    pub extr_typ: Option<String>,
    pub extr_id: Option<String>,
    pub touch_down_id: Option<i32>,
    pub ecid: Option<String>,
    pub ecid_ext: Option<String>,
    pub ecid_extra: Option<HashMap<String, String>>,
    pub uid: Option<String>,
    pub retest_bin_num: Option<String>,
    pub text_dat: Option<String>,
    pub process: Option<String>,
    pub real_wafer_id: Option<String>,
    pub efuse_extra: Option<HashMap<String, String>>,
    pub chip_id: Option<String>,
}

impl RecordBatchWrapper for DieData {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<DieData> = serde_arrow::from_record_batch(batch).unwrap();
        Ok(result)
    }

    fn to_record_batch(data: Vec<Self>) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        let fields = Vec::<FieldRef>::from_type::<DieData>(TracingOptions::default()).unwrap();
        let record_batch = serde_arrow::to_record_batch(&fields, &data).unwrap();
        Ok(record_batch)
    }
}
