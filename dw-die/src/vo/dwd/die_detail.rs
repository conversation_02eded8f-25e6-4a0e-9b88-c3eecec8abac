use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct DieDetail {
    pub id: String,
    pub customer: String,
    pub sub_customer: String,
    pub upload_type: String,
    pub file_id: i64,
    pub file_name: String,
    pub file_type: String,
    pub device_id: String,
    pub factory: String,
    pub factory_site: String,
    pub fab: String,
    pub fab_site: String,
    pub lot_type: String,
    pub lot_id: String,
    pub sblot_id: String,
    pub wafer_lot_id: String,
    pub test_area: String,
    pub test_stage: String,
    pub offline_retest: i32,
    pub offline_retest_ignore_tp: i32,
    pub online_retest: i32,
    pub interrupt: i32,
    pub interrupt_ignore_tp: i32,
    pub dup_retest: i32,
    pub dup_retest_ignore_tp: i32,
    pub batch_num: i32,
    pub batch_num_ignore_tp: i32,
    pub max_offline_retest: i32,
    pub max_online_retest: i32,
    pub is_first_test: i32,
    pub is_final_test: i32,
    pub is_first_test_ignore_tp: i32,
    pub is_final_test_ignore_tp: i32,
    pub is_dup_first_test: i32,
    pub is_dup_final_test: i32,
    pub is_dup_first_test_ignore_tp: i32,
    pub is_dup_final_test_ignore_tp: i32,
    pub num_test: i32,
    pub test_program: String,
    pub test_temperature: String,
    pub test_program_version: String,
    pub spec_nam: String,
    pub spec_ver: String,
    pub hbin_num: i64,
    pub sbin_num: i64,
    pub sbin_pf: String,
    pub sbin_nam: String,
    pub hbin_pf: String,
    pub hbin_nam: String,
    pub hbin: String,
    pub sbin: String,
    pub test_head: i64,
    pub tester_name: String,
    pub tester_type: String,
    pub operator_name: String,
    pub prober_handler_typ: String,
    pub prober_handler_id: String,
    pub probe_card_loadboard_typ: String,
    pub probe_card_loadboard_id: String,
    pub part_flg: String,
    pub part_id: String,
    pub c_part_id: i64,
    pub ecid: String,
    pub ecid_ext: String,
    pub ecid_extra: HashMap<String, String>,
    pub is_standard_ecid: i32,
    pub x_coord: i32,
    pub y_coord: i32,
    pub die_x: i32,
    pub die_y: i32,
    pub test_time: i64,
    pub part_txt: String,
    pub part_fix: String,
    pub site: i64,
    pub site_grp: i64,
    pub site_cnt: i64,
    pub touch_down_id: i32,
    pub site_nums: String,
    pub start_time: i64,
    pub end_time: i64,
    pub start_hour_key: String,
    pub start_day_key: String,
    pub end_hour_key: String,
    pub end_day_key: String,
    pub wafer_id: String,
    pub wafer_no: String,
    pub wafer_size: f64,
    pub wafer_margin: f64,
    pub die_height: f64,
    pub die_width: f64,
    pub wf_units: i64,
    pub wf_flat: String,
    pub center_x: i32,
    pub center_y: i32,
    pub center_offset_x: f64,
    pub center_offset_y: f64,
    pub center_reticle_x: i32,
    pub center_reticle_y: i32,
    pub center_reticle_offset_x: f64,
    pub center_reticle_offset_y: f64,
    pub pos_x: String,
    pub pos_y: String,
    pub die_cnt: i64,
    pub reticle_t_x: i32,
    pub reticle_t_y: i32,
    pub reticle_x: i32,
    pub reticle_y: i32,
    pub reticle_row: i64,
    pub reticle_column: i64,
    pub reticle_row_center_offset: i32,
    pub reticle_column_center_offset: i32,
    pub original_wafer_size: f64,
    pub original_wafer_margin: f64,
    pub original_wf_units: i64,
    pub original_wf_flat: String,
    pub original_pos_x: String,
    pub original_pos_y: String,
    pub original_die_width: f64,
    pub original_die_height: f64,
    pub original_reticle_row: i64,
    pub original_reticle_column: i64,
    pub original_reticle_row_center_offset: i32,
    pub original_reticle_column_center_offset: i32,
    pub original_center_x: i32,
    pub original_center_y: i32,
    pub original_center_reticle_x: i32,
    pub original_center_reticle_y: i32,
    pub original_center_offset_x: f64,
    pub original_center_offset_y: f64,
    pub original_center_reticle_offset_x: f64,
    pub original_center_reticle_offset_y: f64,
    pub site_id: String,
    pub part_cnt: i64,
    pub rtst_cnt: i64,
    pub abrt_cnt: i64,
    pub good_cnt: i64,
    pub func_cnt: i64,
    pub fabwf_id: String,
    pub frame_id: String,
    pub mask_id: String,
    pub wafer_usr_desc: String,
    pub wafer_exc_desc: String,
    pub setup_t: i64,
    pub stat_num: i64,
    pub mode_cod: String,
    pub prot_cod: String,
    pub burn_tim: i64,
    pub cmod_cod: String,
    pub exec_typ: String,
    pub exec_ver: String,
    pub user_txt: String,
    pub aux_file: String,
    pub pkg_typ: String,
    pub family_id: String,
    pub date_cod: String,
    pub facil_id: String,
    pub floor_id: String,
    pub proc_id: String,
    pub oper_frq: String,
    pub flow_id: String,
    pub flow_id_ignore_tp: String,
    pub setup_id: String,
    pub dsgn_rev: String,
    pub eng_id: String,
    pub rom_cod: String,
    pub serl_num: String,
    pub supr_nam: String,
    pub disp_cod: String,
    pub lot_usr_desc: String,
    pub lot_exc_desc: String,
    pub dib_typ: String,
    pub dib_id: String,
    pub cabl_typ: String,
    pub cabl_id: String,
    pub cont_typ: String,
    pub cont_id: String,
    pub lasr_typ: String,
    pub lasr_id: String,
    pub extr_typ: String,
    pub extr_id: String,
    pub retest_bin_num: String,
    pub long_attribute_set: HashMap<String, i64>,
    pub string_attribute_set: HashMap<String, String>,
    pub float_attribute_set: HashMap<String, f64>,
    pub uid: String,
    pub text_dat: String,
    pub create_hour_key: String,
    pub create_day_key: String,
    pub create_time: i64,
    pub create_user: String,
    pub lot_bucket: i32,
    pub is_delete: i32,
    pub process: String,
    pub upload_time: i64,
    pub data_version: i64,
    pub efuse_extra: HashMap<String, String>,
    pub chip_id: String,
}
