use serde::{Deserialize, Serialize};
use serde_json;

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct Properties {
    cp_result_dir: String,
    ft_result_dir: String,
    cp_die_detail_result_dir: String,
    die_detail_result_dir: String,
    cp_die_bitmem_detail_result_dir: String,
    die_bitmem_detail_result_dir: String,
    cp_dim_result_dir_template: String,
    ft_dim_result_dir_template: String,
    dim_result_partition: i32,
    die_detail_result_partition: i32,
    die_bitmem_detail_result_partition: i32,
    ods_db_name: String,
    dwd_db_name: String,
    dim_db_name: String,
    ck_protocol: String,
    ck_address: String,
    ck_query_node_host: String,
    ck_username: String,
    ck_password: String,
    ck_node_host: String,
    ck_node_user: String,
    ck_node_password: String,
    ck_batch_size: String,
    dim_num_partitions: String,
    address: String,
    driver: String,
    username: String,
    password: String,
    fetch_size: String,
    bootstrap_servers: String,
    parquet_block_size: i32,
    insert_cluster_table: bool,
    cp_test_program_clear_flag: bool,
    ft_test_program_clear_flag: bool,
    lot_bucket_num: i32,
    all_die_detail_path: String,
    cp_dws_result_dir_template: String,
    ft_dws_result_dir_template: String,
    dws_result_partition: i32,
    dws_db_name: String,
    ads_db_name: String,
    index_num_partition: String,
    batch_wafermap_config_topic: String,
    jam_limit: i32,
    dl_limit: i32,
    read_wafer_index_table: String,
    device_overall_yield_setting_table: String,
    device_relation_sync_table: String,
    device_overall_yield_pass_bins_sync_table: String,
    wafer_overall_yield_index_num_partition: String,
    batch_product_wafermap_config: String,
    batch_device_wafermap_config: String,
    batch_device_wafermap_config_mapping: String,
    wafermap_config_remove_flying_point_strategy: String,
    wafermap_config_result_dir: String,
    wafermap_config_result_points_partition: i32,
    wafermap_config_result_files_partition: i32,
    write_path: String,
    hdfs_mode: String,
    hdfs_url: String,
    hdfs_user: String,
    attach_threshold: i64,
    assy_lot_overall_yield_detail_path: String,
    assy_lot_overall_yield_detail_partition: i32,
    assy_lot_overall_yield_index_path: String,
    redis_address: String,
    redis_password: String,
}

impl Properties {
    pub fn new(properties: &String) -> Result<Properties, &'static str> {
        let properties: Properties = serde_json::from_str(properties).unwrap();
        Ok(properties)
    }
}
