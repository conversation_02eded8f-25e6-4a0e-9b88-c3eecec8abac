use serde::{Deserialize, Serialize};
use serde_json;

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct TaskParam {
    pub customer: String,
    pub sub_customer: String,
    pub factory: String,
    pub factory_site: String,
    pub test_area: String,
    pub lot_id: String,
    pub wafer_no: String,
    pub lot_type: String,
    pub device_id: String,
    pub execute_mode: String,
    pub file_category: String,
    pub test_stage: String,
    pub upload_time: String,
    pub data_version: String,
    pub die_count: String,
    pub new_data_flag: bool,
}

impl TaskParam {
    pub fn new(param: &String) -> Result<TaskParam, &'static str> {
        let param: TaskParam = serde_json::from_str(param).unwrap();
        Ok(param)
    }
}
