use crate::service::DwdDieService;
use crate::vo::ods::die_data::DieData;
use crate::vo::param::{Properties, TaskParam};
use log::info;

pub struct CpDwdDieService {}

impl DwdDieService for CpDwdDieService {
    fn calculate(dir_path: String, param: TaskParam, properties: Properties) {
        info!("当前正在计算的wafer信息：{:#?}", param);
        let die_data: Vec<DieData> = parquet_provider::parquet_provider::read_parquet(&dir_path);

        info!("读取到的总条数: {}", die_data.len());

        die_data.iter().for_each(|item| {
            println!("a: {:?}, b: {:?}, c: {:?}", item.customer, item.sub_customer, item.file_id);
        });

        // 查询sftp_file_detail对应的文件
        info!("{:#?}", properties);
    }
}
