// 标点符号相关
pub mod mop {
    pub const COMMA: &str = ",";
    pub const SLASH: &str = "/";
}

// stdf字段相关
pub mod stdf_field {
    pub const FILE_CATEGORY: &str = "{FILE_CATEGORY}";
    pub const TEST_AREA: &str = "{TEST_AREA}";
    pub const CUSTOMER: &str = "{CUSTOMER}";
    pub const FACTORY: &str = "{FACTORY}";
    pub const TYPE: &str = "{TYPE}";
    pub const LOT_ID: &str = "{LOT_ID}";
    pub const WAFER_NO: &str = "{WAFER_NO}";
    pub const DEVICE_ID: &str = "{DEVICE_ID}";
    pub const TEST_STAGE: &str = "{TEST_STAGE}";
    pub const LOT_TYPE: &str = "{LOT_TYPE}";
}

// ods类型相关
pub mod ods_type {
    pub const DIE_DATA: &str = "TEST_ITEM_DATA";
    pub const ECID_DATA: &str = "TEST_ITEM_DATA";
    pub const UID_DATA: &str = "TEST_ITEM_DATA";
}
