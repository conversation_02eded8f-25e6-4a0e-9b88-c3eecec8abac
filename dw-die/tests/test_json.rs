mod tests {
    use die_calculater::constant::mop::COMMA;
    use die_calculater::vo::param::{Properties, TaskParam};
    use log::info;
    use std::process;

    #[test]
    fn test_json2vo() {
        let param = "{\"customer\":\"CustomerName\",\"subCustomer\":\"SubCustomerName\",\"factory\":\"FactoryName\",\"factorySite\":\"SiteName\",\"testArea\":\"TestAreaName\",\"lotId\":\"LotID12345\",\"waferNo\":\"WaferNo67890\",\"lotType\":\"TypeA\",\"deviceId\":\"DeviceID54321\",\"executeMode\":\"ModeX\",\"fileCategory\":\"CategoryY\",\"testStage\":\"StageZ\",\"uploadTime\":\"2025-02-28T16:23:00\",\"dataVersion\":\"1.0\",\"dieCount\":\"100\",\"newDataFlag\":true}";
        let param: TaskParam = serde_json::from_str(param).unwrap();
        println!("{:#?}", param);
    }

    #[test]
    fn test_vo2json() {
        let param = TaskParam {
            customer: "CustomerName".into(),
            sub_customer: "SubCustomerName".into(),
            factory: "FactoryName".into(),
            factory_site: "SiteName".into(),
            test_area: "TestAreaName".into(),
            lot_id: "LotID12345".into(),
            wafer_no: "WaferNo67890".into(),
            lot_type: "TypeA".into(),
            device_id: "DeviceID54321".into(),
            execute_mode: "ModeX".into(),
            file_category: "CategoryY".into(),
            test_stage: "StageZ".into(),
            upload_time: "2025-02-28T16:23:00".into(),
            data_version: "1.0".into(),
            die_count: "100".into(),
            new_data_flag: true,
        };
        let serialized = serde_json::to_string_pretty(&param).unwrap();
        println!("Serialized JSON: {}", serialized);
    }

    #[test]
    fn test_parse_param() {
        let args: Vec<String> = vec![String::from("1"), String::from("{\"customer\":\"CustomerName\",\"subCustomer\":\"SubCustomerName\",\"factory\":\"FactoryName\",\"factorySite\":\"SiteName\",\"testArea\":\"TestAreaName\",\"lotId\":\"LotID12345\",\"waferNo\":\"WaferNo67890\",\"lotType\":\"TypeA\",\"deviceId\":\"DeviceID54321\",\"executeMode\":\"ModeX\",\"fileCategory\":\"CategoryY\",\"testStage\":\"StageZ\",\"uploadTime\":\"2025-02-28T16:23:00\",\"dataVersion\":\"1.0\",\"dieCount\":\"100\",\"newDataFlag\":true}"), String::from("{\"cpResultDir\":\"\",\"ftResultDir\":\"\",\"cpDieDetailResultDir\":\"\",\"dieDetailResultDir\":\"\",\"cpDieBitmemDetailResultDir\":\"\",\"dieBitmemDetailResultDir\":\"\",\"cpDimResultDirTemplate\":\"\",\"ftDimResultDirTemplate\":\"\",\"dimResultPartition\":0,\"dieDetailResultPartition\":0,\"dieBitmemDetailResultPartition\":0,\"odsDbName\":\"\",\"dwdDbName\":\"\",\"dimDbName\":\"\",\"ckProtocol\":\"\",\"ckAddress\":\"\",\"ckQueryNodeHost\":\"\",\"ckUsername\":\"\",\"ckPassword\":\"\",\"ckNodeHost\":\"\",\"ckNodeUser\":\"\",\"ckNodePassword\":\"\",\"ckBatchSize\":\"\",\"dimNumPartitions\":\"\",\"address\":\"\",\"driver\":\"\",\"username\":\"\",\"password\":\"\",\"fetchSize\":\"\",\"bootstrapServers\":\"\",\"parquetBlockSize\":0,\"insertClusterTable\":false,\"cpTestProgramClearFlag\":false,\"ftTestProgramClearFlag\":false,\"lotBucketNum\":0,\"allDieDetailPath\":\"\",\"cpDwsResultDirTemplate\":\"\",\"ftDwsResultDirTemplate\":\"\",\"dwsResultPartition\":0,\"dwsDbName\":\"\",\"adsDbName\":\"\",\"indexNumPartition\":\"\",\"batchWafermapConfigTopic\":\"\",\"jamLimit\":0,\"dlLimit\":0,\"readWaferIndexTable\":\"\",\"deviceOverallYieldSettingTable\":\"\",\"deviceRelationSyncTable\":\"\",\"deviceOverallYieldPassBinsSyncTable\":\"\",\"waferOverallYieldIndexNumPartition\":\"\",\"batchProductWafermapConfig\":\"\",\"batchDeviceWafermapConfig\":\"\",\"batchDeviceWafermapConfigMapping\":\"\",\"wafermapConfigRemoveFlyingPointStrategy\":\"\",\"wafermapConfigResultDir\":\"\",\"wafermapConfigResultPointsPartition\":0,\"wafermapConfigResultFilesPartition\":0,\"writePath\":\"\",\"hdfsMode\":\"\",\"hdfsUrl\":\"\",\"hdfsUser\":\"\",\"attachThreshold\":0,\"assyLotOverallYieldDetailPath\":\"\",\"assyLotOverallYieldDetailPartition\":0,\"assyLotOverallYieldIndexPath\":\"\",\"redisAddress\":\"\",\"redisPassword\":\"\"}")];
        // 赋值
        let param = TaskParam::new(&args[1]).unwrap_or_else(|err| {
            println!("处理参数出错: {}, 具体参数为: {:?}", err, args.join(COMMA));
            process::exit(1);
        });
        let properties = Properties::new(&args[2]).unwrap_or_else(|err| {
            info!("处理参数出错: {}, 具体参数为: {}", err, args[2]);
            process::exit(1);
        });
        println!("{:#?}", param);
        println!("{:#?}", properties);
    }
}
