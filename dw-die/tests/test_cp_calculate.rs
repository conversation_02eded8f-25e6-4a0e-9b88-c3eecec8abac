mod tests {
    use die_calculater::service::cp_dwd_die_service::CpDwdDieService;
    use die_calculater::service::cp_dws_die_service::CpDwsDieService;
    use die_calculater::service::DwdDieService;
    use die_calculater::service::DwsDieService;
    use die_calculater::vo::param::{Properties, TaskParam};
    use log::info;
    use std::process;

    #[test]
    fn test_cp_dwd_die_service() {
        env_logger::init();
        let args: Vec<String> = vec![String::from("1"), String::from("{\"customer\":\"CustomerName\",\"subCustomer\":\"SubCustomerName\",\"factory\":\"FactoryName\",\"factorySite\":\"SiteName\",\"testArea\":\"TestAreaName\",\"lotId\":\"LotID12345\",\"waferNo\":\"WaferNo67890\",\"lotType\":\"TypeA\",\"deviceId\":\"DeviceID54321\",\"executeMode\":\"ModeX\",\"fileCategory\":\"CategoryY\",\"testStage\":\"StageZ\",\"uploadTime\":\"2025-02-28T16:23:00\",\"dataVersion\":\"1.0\",\"dieCount\":\"100\",\"newDataFlag\":true}"), String::from("{\"cpResultDir\":\"\",\"ftResultDir\":\"\",\"cpDieDetailResultDir\":\"\",\"dieDetailResultDir\":\"\",\"cpDieBitmemDetailResultDir\":\"\",\"dieBitmemDetailResultDir\":\"\",\"cpDimResultDirTemplate\":\"\",\"ftDimResultDirTemplate\":\"\",\"dimResultPartition\":0,\"dieDetailResultPartition\":0,\"dieBitmemDetailResultPartition\":0,\"odsDbName\":\"\",\"dwdDbName\":\"\",\"dimDbName\":\"\",\"ckProtocol\":\"\",\"ckAddress\":\"\",\"ckQueryNodeHost\":\"\",\"ckUsername\":\"\",\"ckPassword\":\"\",\"ckNodeHost\":\"\",\"ckNodeUser\":\"\",\"ckNodePassword\":\"\",\"ckBatchSize\":\"\",\"dimNumPartitions\":\"\",\"address\":\"\",\"driver\":\"\",\"username\":\"\",\"password\":\"\",\"fetchSize\":\"\",\"bootstrapServers\":\"\",\"parquetBlockSize\":0,\"insertClusterTable\":false,\"cpTestProgramClearFlag\":false,\"ftTestProgramClearFlag\":false,\"lotBucketNum\":0,\"allDieDetailPath\":\"\",\"cpDwsResultDirTemplate\":\"\",\"ftDwsResultDirTemplate\":\"\",\"dwsResultPartition\":0,\"dwsDbName\":\"\",\"adsDbName\":\"\",\"indexNumPartition\":\"\",\"batchWafermapConfigTopic\":\"\",\"jamLimit\":0,\"dlLimit\":0,\"readWaferIndexTable\":\"\",\"deviceOverallYieldSettingTable\":\"\",\"deviceRelationSyncTable\":\"\",\"deviceOverallYieldPassBinsSyncTable\":\"\",\"waferOverallYieldIndexNumPartition\":\"\",\"batchProductWafermapConfig\":\"\",\"batchDeviceWafermapConfig\":\"\",\"batchDeviceWafermapConfigMapping\":\"\",\"wafermapConfigRemoveFlyingPointStrategy\":\"\",\"wafermapConfigResultDir\":\"\",\"wafermapConfigResultPointsPartition\":0,\"wafermapConfigResultFilesPartition\":0,\"writePath\":\"\",\"hdfsMode\":\"\",\"hdfsUrl\":\"\",\"hdfsUser\":\"\",\"attachThreshold\":0,\"assyLotOverallYieldDetailPath\":\"\",\"assyLotOverallYieldDetailPartition\":0,\"assyLotOverallYieldIndexPath\":\"\",\"redisAddress\":\"\",\"redisPassword\":\"\"}")];
        let param = TaskParam::new(&args[1]).unwrap_or_else(|err| {
            info!("处理参数出错: {}, 具体参数为: {}", err, args[1]);
            process::exit(1);
        });
        let properties = Properties::new(&args[2]).unwrap_or_else(|err| {
            info!("处理参数出错: {}, 具体参数为: {}", err, args[2]);
            process::exit(1);
        });
        CpDwdDieService::calculate(String::from("/Users/<USER>/Desktop/data"), param, properties);
    }

    #[test]
    fn test_cp_dws_die_service() {
        env_logger::init();
        let args: Vec<String> = vec![String::from("1"), String::from("{\"customer\":\"CustomerName\",\"subCustomer\":\"SubCustomerName\",\"factory\":\"FactoryName\",\"factorySite\":\"SiteName\",\"testArea\":\"TestAreaName\",\"lotId\":\"LotID12345\",\"waferNo\":\"WaferNo67890\",\"lotType\":\"TypeA\",\"deviceId\":\"DeviceID54321\",\"executeMode\":\"ModeX\",\"fileCategory\":\"CategoryY\",\"testStage\":\"StageZ\",\"uploadTime\":\"2025-02-28T16:23:00\",\"dataVersion\":\"1.0\",\"dieCount\":\"100\",\"newDataFlag\":true}"), String::from("{\"cpResultDir\":\"\",\"ftResultDir\":\"\",\"cpDieDetailResultDir\":\"\",\"dieDetailResultDir\":\"\",\"cpDieBitmemDetailResultDir\":\"\",\"dieBitmemDetailResultDir\":\"\",\"cpDimResultDirTemplate\":\"\",\"ftDimResultDirTemplate\":\"\",\"dimResultPartition\":0,\"dieDetailResultPartition\":0,\"dieBitmemDetailResultPartition\":0,\"odsDbName\":\"\",\"dwdDbName\":\"\",\"dimDbName\":\"\",\"ckProtocol\":\"\",\"ckAddress\":\"\",\"ckQueryNodeHost\":\"\",\"ckUsername\":\"\",\"ckPassword\":\"\",\"ckNodeHost\":\"\",\"ckNodeUser\":\"\",\"ckNodePassword\":\"\",\"ckBatchSize\":\"\",\"dimNumPartitions\":\"\",\"address\":\"\",\"driver\":\"\",\"username\":\"\",\"password\":\"\",\"fetchSize\":\"\",\"bootstrapServers\":\"\",\"parquetBlockSize\":0,\"insertClusterTable\":false,\"cpTestProgramClearFlag\":false,\"ftTestProgramClearFlag\":false,\"lotBucketNum\":0,\"allDieDetailPath\":\"\",\"cpDwsResultDirTemplate\":\"\",\"ftDwsResultDirTemplate\":\"\",\"dwsResultPartition\":0,\"dwsDbName\":\"\",\"adsDbName\":\"\",\"indexNumPartition\":\"\",\"batchWafermapConfigTopic\":\"\",\"jamLimit\":0,\"dlLimit\":0,\"readWaferIndexTable\":\"\",\"deviceOverallYieldSettingTable\":\"\",\"deviceRelationSyncTable\":\"\",\"deviceOverallYieldPassBinsSyncTable\":\"\",\"waferOverallYieldIndexNumPartition\":\"\",\"batchProductWafermapConfig\":\"\",\"batchDeviceWafermapConfig\":\"\",\"batchDeviceWafermapConfigMapping\":\"\",\"wafermapConfigRemoveFlyingPointStrategy\":\"\",\"wafermapConfigResultDir\":\"\",\"wafermapConfigResultPointsPartition\":0,\"wafermapConfigResultFilesPartition\":0,\"writePath\":\"\",\"hdfsMode\":\"\",\"hdfsUrl\":\"\",\"hdfsUser\":\"\",\"attachThreshold\":0,\"assyLotOverallYieldDetailPath\":\"\",\"assyLotOverallYieldDetailPartition\":0,\"assyLotOverallYieldIndexPath\":\"\",\"redisAddress\":\"\",\"redisPassword\":\"\"}")];
        let param = TaskParam::new(&args[1]).unwrap_or_else(|err| {
            info!("处理参数出错: {}, 具体参数为: {}", err, args[1]);
            process::exit(1);
        });
        let properties = Properties::new(&args[2]).unwrap_or_else(|err| {
            info!("处理参数出错: {}, 具体参数为: {}", err, args[2]);
            process::exit(1);
        });
        let die_detail = Vec::new();
        CpDwsDieService::calculate(&die_detail, &param, &properties);
    }
}
