mod tests {
    use arrow::datatypes::Field;
    use serde::{Deserialize, Serialize};
    use serde_arrow::schema::{Schema<PERSON>ike, TracingOptions};
    use std::sync::Arc;

    #[test]
    fn test() -> serde_arrow::Result<()> {

        let (_, arrays) = serde_arrow::_impl::docs::defs::example_arrow_arrays();
        #[derive(Deserialize, Serialize)]
        struct Record {
            a: Option<f32>,
            b: u64,
        }

        let fields: Vec<Arc<Field>> = Vec::<Field>::from_type::<Record>(TracingOptions::default())?
            .into_iter()
            .map(Arc::new)
            .collect();
        let items: Vec<Record> = serde_arrow::from_arrow(&fields, &arrays)?;

        items.iter().for_each(|item| {
            println!("a: {:?}, b: {:?}", item.a, item.b);
        });

        let record_batch = serde_arrow::to_record_batch(&fields, &items)?;
        println!("record_batch: {:?}", record_batch);

        let items: Vec<Record> = serde_arrow::from_record_batch(&record_batch)?;

        items.iter().for_each(|item| {
            println!("a: {:?}, b: {:?}", item.a, item.b);
        });

        Ok(())
    }
}