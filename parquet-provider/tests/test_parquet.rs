mod tests {
    use parquet::arrow::arrow_reader::ParquetRecordBatchReaderBuilder;
    use std::fs::File;
    use std::fs::{self, DirEntry};
    use std::path::{Path, PathBuf};

    #[test]
    fn test_read_parquet() {
        let dir_path = "/Users/<USER>/Desktop/data";
        let parquet_files = find_parquet_files(dir_path).unwrap();

        // 读取并合并所有RecordBatch
        let mut all_data = Vec::new();
        for file_path in parquet_files {
            let file = File::open(file_path).unwrap();
            let reader_builder = ParquetRecordBatchReaderBuilder::try_new(file).unwrap();
            let mut reader = reader_builder.build().unwrap();
            while let Some(batch) = reader.next() {
                all_data.push(batch.unwrap());
            }
        }

        println!("{}", all_data.len());
    }

    fn find_parquet_files(dir: &str) -> Result<Vec<PathBuf>, Box<dyn std::error::Error>> {
        let mut result = Vec::new();
        visit_dirs(Path::new(dir), &mut |entry| {
            if let Some(ext) = entry.path().extension() {
                if ext == "parquet" {
                    result.push(entry.path().to_path_buf());
                }
            }
        })?;
        Ok(result)
    }

    fn visit_dirs(dir: &Path, cb: &mut dyn FnMut(&DirEntry)) -> std::io::Result<()> {
        if dir.is_dir() {
            for entry in fs::read_dir(dir)? {
                let entry = entry?;
                let path = entry.path();
                if path.is_dir() {
                    visit_dirs(&path, cb)?; // 递归调用
                } else {
                    cb(&entry);
                }
            }
        }
        Ok(())
    }
}
