use crate::constant::file_type::PARQUET;
use crate::hdfs_provider::{HdfsConfig, Hdfs<PERSON>rror, HdfsProvider};
use crate::RecordBatchWrapper;
use log::{error, info};
use parquet::arrow::arrow_reader::ParquetRecordBatchReaderBuilder;
use parquet::basic::{Compression, ZstdLevel};
use parquet::file::properties::WriterProperties;
use rayon::prelude::*;
use std::fs::File;
use std::fs::{self, DirEntry};
use std::path::{Path, PathBuf};
use thiserror::Error;
use uuid::Uuid;

#[derive(Error, Debug)]
pub enum ParquetProviderError {
    #[error("IO error: {0}")]
    IOError(#[from] std::io::Error),
    #[error("Parquet error: {0}")]
    ParquetError(#[from] parquet::errors::ParquetError),
    #[error("Arrow error: {0}")]
    ArrowError(#[from] arrow::error::ArrowError),
    #[error("HDFS error: {0}")]
    HdfsError(#[from] HdfsError),
    #[error("Data conversion error: {0}")]
    DataConversionError(String),
    #[error("Empty data error: {0}")]
    EmptyDataError(String),
    #[error("File operation error: {0}")]
    FileOperationError(String),
    #[error("Runtime error: {0}")]
    RuntimeError(String),
}

pub async fn write_parquet_multi<T>(
    dir_path: &str,
    datas: &Vec<&Vec<T>>,
    hdfs_config: Option<&HdfsConfig>,
    batch_size: usize,
) -> Result<(), ParquetProviderError>
where
    T: RecordBatchWrapper + Send + Sync + Clone + 'static,
{
    if datas.is_empty() {
        return Err(ParquetProviderError::EmptyDataError(
            "Cannot write parquet files with empty data vector".to_string(),
        ));
    }

    // 生成临时目录路径
    let temp_uuid = Uuid::new_v4().to_string();
    let temp_dir = format!("/tmp/deploy/onedata/dataware/dataware-dw-test-item/data/write/{}", temp_uuid);
    info!("write_parquet_multi to tmp dir: {}", temp_dir);

    // 确保在函数结束时清理临时目录
    let _cleanup_guard = TempDirCleanup::new(&temp_dir);

    // 创建临时目录
    fs::create_dir_all(&temp_dir).map_err(|e| {
        ParquetProviderError::FileOperationError(format!("Failed to create temp directory {}: {}", temp_dir, e))
    })?;

    datas
        .par_iter()
        .enumerate()
        .map(|(index, data)| {
            if data.is_empty() {
                return Err(ParquetProviderError::EmptyDataError(format!(
                    "Cannot write parquet file with empty data at index: {}",
                    index
                )));
            }

            let file_name = format!("part-{:04}.parquet", index);
            let temp_file_path = format!("{}/{}", temp_dir, file_name);
            let data_clone = (*data).clone(); // Clone the data for the task

            // 生成第一个记录的 schema
            let head_record_batch = T::to_record_batch(&data_clone[0..1]).map_err(|s| {
                ParquetProviderError::DataConversionError(format!(
                    "Can not get record batch from first data at index {}: {}",
                    index, s
                ))
            })?;
            let schema = head_record_batch.schema();
            let file = File::create(&temp_file_path).map_err(|e| {
                ParquetProviderError::FileOperationError(format!(
                    "Failed to create temp file {}: {}",
                    temp_file_path, e
                ))
            })?;

            let props = WriterProperties::builder()
                .set_compression(Compression::ZSTD(ZstdLevel::default()))
                .set_dictionary_enabled(true)
                .set_max_row_group_size(1 * 1024 * 1024)
                .build();

            let mut writer = parquet::arrow::ArrowWriter::try_new(file, schema, Some(props))
                .map_err(|e| ParquetProviderError::ParquetError(e))?;

            for chunk in data_clone.chunks(batch_size) {
                let record_batch = T::to_record_batch(chunk).map_err(|e| {
                    ParquetProviderError::DataConversionError(format!("Failed to convert data to record batch: {}", e))
                })?;

                info!("Writing part-{:04}.parquet, record_batch num_rows: {:?}", index, record_batch.num_rows());

                if record_batch.num_rows() == 0 {
                    return Err(ParquetProviderError::EmptyDataError(format!(
                        "Cannot write parquet file with empty record batch: {}",
                        temp_file_path
                    )));
                }

                writer.write(&record_batch).map_err(|e| ParquetProviderError::ParquetError(e))?;
            }

            writer.close().map_err(|e| ParquetProviderError::ParquetError(e))?;
            info!("Successfully wrote parquet data to temp file: {}", temp_file_path);

            Ok(())
        })
        .collect::<Result<Vec<()>, ParquetProviderError>>()?;

    info!("All parquet files written successfully to temp directory");

    // 根据hdfs_config决定后续操作
    if let Some(config) = hdfs_config {
        // 使用HDFS：上传整个目录到HDFS
        let hdfs_provider = HdfsProvider::new(config.clone())?;
        hdfs_provider.upload(&temp_dir, dir_path).await?;
        info!("Successfully uploaded all parquet files to HDFS: {}", dir_path);
    } else {
        // 本地操作：移动所有文件到目标目录
        fs::create_dir_all(dir_path).map_err(|e| {
            ParquetProviderError::FileOperationError(format!("Failed to create target directory {}: {}", dir_path, e))
        })?;

        // 移动所有生成的parquet文件
        for entry in fs::read_dir(&temp_dir).map_err(|e| {
            ParquetProviderError::FileOperationError(format!("Failed to read temp directory {}: {}", temp_dir, e))
        })? {
            let entry = entry.map_err(|e| {
                ParquetProviderError::FileOperationError(format!("Failed to read directory entry: {}", e))
            })?;

            if let Some(ext) = entry.path().extension() {
                if ext == PARQUET {
                    let source_path = entry.path();
                    let file_name = entry.file_name();
                    let target_path = Path::new(dir_path).join(file_name);

                    fs::rename(&source_path, &target_path).map_err(|e| {
                        ParquetProviderError::FileOperationError(format!(
                            "Failed to move file from {:?} to {:?}: {}",
                            source_path, target_path, e
                        ))
                    })?;
                    info!("Successfully moved parquet file to: {:?}", target_path);
                }
            }
        }
    }

    Ok(())
}

pub async fn write_parquet<T>(
    dir_path: &str,
    file_name: &str,
    data: &Vec<T>,
    hdfs_config: Option<&HdfsConfig>,
) -> Result<(), ParquetProviderError>
where
    T: RecordBatchWrapper,
{
    let full_path = format!("{}/{}", dir_path.trim_end_matches('/'), file_name);
    if data.is_empty() {
        return Err(ParquetProviderError::EmptyDataError(format!(
            "Cannot write parquet file with empty data: {}",
            full_path
        )));
    }

    // 生成临时目录路径
    let temp_uuid = Uuid::new_v4().to_string();
    let temp_dir = format!("/tmp/deploy/onedata/dataware/dataware-dw-test-item/data/write/{}", temp_uuid);
    info!("write_parquet to tmp dir: {}", temp_dir);
    let temp_file_path = format!("{}/{}", temp_dir, file_name);

    // 确保在函数结束时清理临时目录
    let _cleanup_guard = TempDirCleanup::new(&temp_dir);

    // 创建临时目录
    fs::create_dir_all(&temp_dir).map_err(|e| {
        ParquetProviderError::FileOperationError(format!("Failed to create temp directory {}: {}", temp_dir, e))
    })?;

    // 写入数据到临时文件
    let file = File::create(&temp_file_path).map_err(|e| {
        ParquetProviderError::FileOperationError(format!("Failed to create temp file {}: {}", temp_file_path, e))
    })?;

    let props = WriterProperties::builder()
        .set_compression(Compression::ZSTD(ZstdLevel::default()))
        .set_dictionary_enabled(true)
        .set_max_row_group_size(1 * 1024 * 1024)
        .build();
    let head_record_batch = T::to_record_batch(&data[0..1]).map_err(|s| {
        ParquetProviderError::DataConversionError(format!("Can not get record batch from first data: {}", s))
    })?;

    let mut writer = parquet::arrow::ArrowWriter::try_new(file, head_record_batch.schema(), Some(props))
        .map_err(|e| ParquetProviderError::ParquetError(e))?;

    for chunk in data.chunks(500000) {
        let record_batch = T::to_record_batch(chunk).map_err(|e| {
            ParquetProviderError::DataConversionError(format!("Failed to convert data to record batch: {}", e))
        })?;

        info!("record_batch num_rows: {:?}", record_batch.num_rows());

        if record_batch.num_rows() == 0 {
            return Err(ParquetProviderError::EmptyDataError(format!(
                "Cannot write parquet file with empty record batch: {}",
                full_path
            )));
        }

        writer.write(&record_batch).map_err(|e| ParquetProviderError::ParquetError(e))?;
    }

    writer.close().map_err(|e| ParquetProviderError::ParquetError(e))?;

    info!("Successfully wrote parquet data to temp file: {}", temp_file_path);

    // 根据hdfs_config决定后续操作
    if let Some(config) = hdfs_config {
        // 使用HDFS：上传到HDFS
        let hdfs_provider = HdfsProvider::new(config.clone())?;
        hdfs_provider.upload(&temp_file_path, dir_path).await?;
        info!("Successfully uploaded parquet file to HDFS: {}", dir_path);
    } else {
        // 本地操作：移动到目标目录
        fs::create_dir_all(dir_path).map_err(|e| {
            ParquetProviderError::FileOperationError(format!("Failed to create target directory {}: {}", dir_path, e))
        })?;

        fs::rename(&temp_file_path, &full_path).map_err(|e| {
            ParquetProviderError::FileOperationError(format!(
                "Failed to move file from {} to {}: {}",
                temp_file_path, full_path, e
            ))
        })?;
        info!("Successfully moved parquet file to: {}", full_path);
    }

    Ok(())
}

pub async fn read_parquet_multi<T>(
    dir_path: &str,
    hdfs_config: Option<&HdfsConfig>,
) -> Result<Vec<Vec<T>>, ParquetProviderError>
where
    T: RecordBatchWrapper + Send + Sync + 'static,
{
    // 使用HDFS：下载到临时目录
    let temp_uuid = Uuid::new_v4().to_string();
    let temp_dir = format!("/tmp/deploy/onedata/dataware/dataware-dw-test-item/data/read/{}", temp_uuid);
    // 确保在函数结束时清理临时目录
    let _cleanup_guard = TempDirCleanup::new(&temp_dir);
    let actual_read_path = if let Some(config) = hdfs_config {
        info!("read_parquet_multi to tmp dir: {}", temp_dir);

        // 创建临时目录
        fs::create_dir_all(&temp_dir).map_err(|e| {
            ParquetProviderError::FileOperationError(format!("Failed to create temp directory {}: {}", temp_dir, e))
        })?;

        let hdfs_provider = HdfsProvider::new(config.clone())?;
        hdfs_provider.download(dir_path, &temp_dir).await?;
        info!("Successfully downloaded parquet data from HDFS to temp dir: {}", temp_dir);

        temp_dir
    } else {
        // 本地操作：直接使用给定路径
        dir_path.to_string()
    };

    // 读取parquet文件
    let mut parquet_files = find_parquet_files(&actual_read_path).map_err(|e| {
        ParquetProviderError::FileOperationError(format!("Failed to find parquet files in {}: {}", actual_read_path, e))
    })?;

    if parquet_files.is_empty() {
        return Err(ParquetProviderError::EmptyDataError(format!(
            "No parquet files found in directory: {}",
            actual_read_path
        )));
    }

    // 按文件名排序，确保part-0000.parquet, part-0001.parquet等按顺序读取
    parquet_files.sort();

    info!("Found {} parquet files to read", parquet_files.len());

    let final_results = parquet_files
        .into_par_iter()
        .enumerate()
        .map(|(index, file_path)| {
            info!("Reading parquet file {}: {}", index, file_path.display());

            let file = File::open(&file_path).map_err(|e| {
                ParquetProviderError::FileOperationError(format!("Failed to open parquet file {:?}: {}", file_path, e))
            })?;

            let reader_builder =
                ParquetRecordBatchReaderBuilder::try_new(file).map_err(|e| ParquetProviderError::ParquetError(e))?;

            let mut reader = reader_builder.build().map_err(|e| ParquetProviderError::ParquetError(e))?;

            let mut all_batch = Vec::new();
            while let Some(batch_result) = reader.next() {
                let batch = batch_result.map_err(|e| ParquetProviderError::ArrowError(e))?;
                all_batch.push(batch);
            }

            if all_batch.is_empty() {
                return Ok(Vec::new());
            }

            let mut result = Vec::new();
            for batch in &all_batch {
                let records = T::from_record_batch(batch).map_err(|e| {
                    ParquetProviderError::DataConversionError(format!("Failed to convert record batch to data: {}", e))
                })?;
                result.extend(records);
            }

            info!("Successfully read {} records from file {}: {}", result.len(), index, file_path.display());
            Ok(result)
        })
        .collect::<Result<Vec<Vec<T>>, ParquetProviderError>>()?;

    let total_records: usize = final_results.iter().map(|v| v.len()).sum();
    info!(
        "Successfully read {} files with total {} records from directory: {}",
        final_results.len(),
        total_records,
        dir_path
    );

    Ok(final_results)
}

pub async fn read_parquet<T>(dir_path: &str, hdfs_config: Option<&HdfsConfig>) -> Result<Vec<T>, ParquetProviderError>
where
    T: RecordBatchWrapper,
{
    // 使用HDFS：下载到临时目录
    let temp_uuid = Uuid::new_v4().to_string();
    let temp_dir = format!("/tmp/deploy/onedata/dataware/dataware-dw-test-item/data/write/{}", temp_uuid);
    // 确保在函数结束时清理临时目录
    let _cleanup_guard = TempDirCleanup::new(&temp_dir);
    let actual_read_path = if let Some(config) = hdfs_config {
        info!("read_parquet to tmp dir: {}", temp_dir);

        // 创建临时目录
        fs::create_dir_all(&temp_dir).map_err(|e| {
            ParquetProviderError::FileOperationError(format!("Failed to create temp directory {}: {}", temp_dir, e))
        })?;

        let hdfs_provider = HdfsProvider::new(config.clone())?;
        hdfs_provider.download(dir_path, &temp_dir).await?;
        info!("Successfully downloaded parquet data from HDFS to temp dir: {}", temp_dir);

        temp_dir
    } else {
        // 本地操作：直接使用给定路径
        dir_path.to_string()
    };

    // 读取parquet文件
    let parquet_files = find_parquet_files(&actual_read_path).map_err(|e| {
        ParquetProviderError::FileOperationError(format!("Failed to find parquet files in {}: {}", actual_read_path, e))
    })?;

    if parquet_files.is_empty() {
        return Err(ParquetProviderError::EmptyDataError(format!(
            "No parquet files found in directory: {}",
            actual_read_path
        )));
    }

    // 读取并合并所有RecordBatch
    let mut all_batch = Vec::new();
    for file_path in parquet_files {
        info!("读取parquet文件: {}", file_path.display());
        let file = File::open(&file_path).map_err(|e| {
            ParquetProviderError::FileOperationError(format!("Failed to open parquet file {:?}: {}", file_path, e))
        })?;

        let reader_builder =
            ParquetRecordBatchReaderBuilder::try_new(file).map_err(|e| ParquetProviderError::ParquetError(e))?;

        let mut reader = reader_builder.build().map_err(|e| ParquetProviderError::ParquetError(e))?;

        while let Some(batch_result) = reader.next() {
            let batch = batch_result.map_err(|e| ParquetProviderError::ArrowError(e))?;
            all_batch.push(batch);
        }
    }
    info!("parquet文件读取完成: {},开始转换成实体类", dir_path);
    if all_batch.is_empty() {
        return Err(ParquetProviderError::EmptyDataError("No record batches read from parquet files".to_string()));
    }

    let mut result = Vec::new();
    for batch in &all_batch {
        // info!("读取record batch: {}", batch.num_rows());
        let records = T::from_record_batch(batch).map_err(|e| {
            ParquetProviderError::DataConversionError(format!("Failed to convert record batch to data: {}", e))
        })?;
        result.extend(records);
    }

    info!("读取出: {}条记录", result.len());
    Ok(result)
}

fn find_parquet_files(dir: &str) -> Result<Vec<PathBuf>, Box<dyn std::error::Error>> {
    let mut result = Vec::new();
    visit_dirs(Path::new(dir), &mut |entry| {
        if let Some(ext) = entry.path().extension() {
            if ext == PARQUET {
                result.push(entry.path().to_path_buf());
            }
        }
    })?;
    Ok(result)
}

fn visit_dirs(dir: &Path, cb: &mut dyn FnMut(&DirEntry)) -> std::io::Result<()> {
    if dir.is_dir() {
        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_dir() {
                visit_dirs(&path, cb)?;
            } else {
                cb(&entry);
            }
        }
    }
    Ok(())
}

/// 临时目录清理守卫，确保在作用域结束时自动清理临时目录
struct TempDirCleanup {
    temp_dir: String,
}

impl TempDirCleanup {
    fn new(temp_dir: &str) -> Self {
        Self { temp_dir: temp_dir.to_string() }
    }
}

impl Drop for TempDirCleanup {
    fn drop(&mut self) {
        if Path::new(&self.temp_dir).exists() {
            if let Err(e) = fs::remove_dir_all(&self.temp_dir) {
                error!("Failed to cleanup temp directory {}: {}", self.temp_dir, e);
            } else {
                info!("Successfully cleaned up temp directory: {}", self.temp_dir);
            }
        }
    }
}
