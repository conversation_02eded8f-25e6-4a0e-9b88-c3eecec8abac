use common::dto::ods::test_item_data_parquet::TestItemDataParquet;
use env_logger;
use parquet_provider::{hdfs_provider::{HdfsConfig, HdfsProvider}, parquet_provider::{read_parquet, write_parquet}};
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[tokio::main]
async fn main() {
    unsafe { std::env::set_var("RUST_BACKTRACE", "full"); }
    color_eyre::install().unwrap();
    tracing_subscriber::fmt()
        .with_max_level(Level::TRACE)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .init();
    
    let hdfs_config = HdfsConfig::default();
    let hdfs_provider = HdfsProvider::new_default().unwrap();
    let files = hdfs_provider.list_files("/user/glory/data/onedata/dataware/ods/result/STDF/TEST_ITEM_DATA/TEST_AREA=FT/CUSTOMER=GUWAVE/FACTORY=CZ_343_002/DEVICE_ID=cz343002/LOT_ID=gubocz343002/TEST_STAGE=FT1/LOT_TYPE=PRODUCTION",true).await.unwrap();
    println!("end {:?}", files);
    // let down = hdfs_provider.download("/user/glory/data/onedata/dataware/ods/result/STDF/TEST_ITEM_DATA/TEST_AREA=FT/CUSTOMER=GUWAVE/FACTORY=CZ_343_002/DEVICE_ID=cz343002/LOT_ID=gubocz343002/TEST_STAGE=FT1/LOT_TYPE=PRODUCTION/FILE_ID=667243/part-3_gubo_demo_2025-07-22_13_50_03.stdf.test_item_data.parquet",
    //                                        "/tmp/0730").await;
    // match down {
    //     Ok(_) => println!("下载成功"),
    //     Err(e) => eprintln!("下载失败: {:?}", e),
    // }
    // let upload = hdfs_provider
    //     .upload(
    //         "/tmp/0730/part-3_gubo_demo_2025-07-22_13_50_03.stdf.test_item_data.parquet",
    //         "/user/glory/tmp/tmp0730/"
    //     )
    //     .await.unwrap();

    let res = read_parquet::<TestItemDataParquet>("/user/glory/data/onedata/dataware/ods/result/STDF/TEST_ITEM_DATA/TEST_AREA=FT/CUSTOMER=GUWAVE/FACTORY=CZ_343_002/DEVICE_ID=cz343002/LOT_ID=gubocz343002/TEST_STAGE=FT1/LOT_TYPE=PRODUCTION", Some(&hdfs_config)).await.unwrap();
    // println!("{:?}", res);

    write_parquet("/user/glory/tmp/0731","data.parquet", &res, Some(&hdfs_config)).await.unwrap();
    ()
}
