use color_eyre::Result;
use opendal::services;
use opendal::Operator;
use parquet::arrow::arrow_reader::ParquetRecordBatchReaderBuilder;

use std::env;
use std::path::Path;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;
use walkdir::WalkDir;

#[tokio::main]
async fn main() -> Result<()> {
    unsafe { std::env::set_var("RUST_BACKTRACE", "full"); }
    color_eyre::install().unwrap();
    tracing_subscriber::fmt()
        .with_max_level(Level::TRACE)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .init();

    let mut config = services::HdfsConfig::default();
    unsafe { std::env::set_var("CLASSPATH", generate_hadoop_classpath(Path::new("/Users/<USER>/Downloads/hadoop"))); }
    unsafe { std::env::set_var("HADOOP_CONF_DIR", "/Users/<USER>/Downloads/hadoop/conf"); }
    unsafe { std::env::set_var("HADOOP_HOME", "/Users/<USER>/Downloads/hadoop"); }
    config.name_node = Some("hdfs://192.168.2.171:8020".to_string());
    // config.root = Some("/Users/<USER>/Downloads/hadoop".to_string());
    let op: Operator = Operator::from_config(config).unwrap().finish();
    // 3. 读取文件内容
    let path = "/user/glory/data/onedata/dataware/ods/result/STDF/TEST_ITEM_DATA/TEST_AREA=FT/CUSTOMER=GUWAVE/FACTORY=CZ_343_002/DEVICE_ID=cz343002/LOT_ID=gubocz343002/TEST_STAGE=FT1/LOT_TYPE=PRODUCTION/FILE_ID=667243/part-3_gubo_demo_2025-07-22_13_50_03.stdf.test_item_data.parquet";
    println!("从 HDFS 读取文件: {}/{}", op.info().root(), path);
    let data = op.read(path).await?;
    // let mut file = tokio::fs::File::create("/Users/<USER>/Downloads/b.parquet").await?;
    // file.write_all(&data.to_bytes()).await?;

    // 4. 生成batch
    let mut parquet = ParquetRecordBatchReaderBuilder::try_new(data.to_bytes())
        .unwrap()
        .build()
        .unwrap();
    let batch = parquet.next().unwrap().unwrap();

    println!("batch: {:?}", batch);
    Ok(())
}

fn generate_hadoop_classpath(hadoop_home: &Path) -> String {
    // 1. 递归地遍历目录，找到所有 .jar 文件。
    let jar_files: Vec<String> = WalkDir::new(hadoop_home)
        .into_iter()
        // filter_map 会过滤掉遍历过程中产生的错误（比如权限问题）
        .filter_map(|entry| entry.ok())
        .filter(|entry| {
            // 确保我们只处理文件
            if !entry.file_type().is_file() {
                return false;
            }
            // 获取文件路径的扩展名
            match entry.path().extension() {
                // 如果扩展名是 "jar" (不区分大小写)
                Some(ext) => ext.to_string_lossy().eq_ignore_ascii_case("jar"),
                None => false,
            }
        })
        // 将 PathBuf 转换为 String
        .map(|entry| entry.path().to_string_lossy().into_owned())
        .collect();

    let path_separator = ":";

    let hadoop_jars_path = jar_files.join(path_separator);

    // 3. 获取已有的 CLASSPATH 环境变量。
    // 如果 CLASSPATH 不存在，则返回一个空字符串。
    let existing_classpath = env::var("CLASSPATH").unwrap_or_else(|_| String::new());

    // 4. 组合新的路径和旧的路径。
    if hadoop_jars_path.is_empty() {
        // 如果没有找到任何 JAR 文件，则直接返回旧的 CLASSPATH
        existing_classpath
    } else if existing_classpath.is_empty() {
        // 如果旧的 CLASSPATH 为空，则只返回新的 JAR 路径
        hadoop_jars_path
    } else {
        // 将新的路径追加到旧的 CLASSPATH 前面
        format!("{}{}{}", hadoop_jars_path, path_separator, existing_classpath)
    }
}
