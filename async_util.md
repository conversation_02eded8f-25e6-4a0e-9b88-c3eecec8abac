异步计算与消费控制工具设计文档

1. 概述

本文档描述了一种通用的异步计算与消费控制工具的设计方法，该方法可以用于需要控制计算和消费速率的场景。核心思想是通过异步通道（async channel）实现生产者-消费者模式，当消费速率跟不上计算速率时，自动暂停计算，直到消费者处理完部分数据后再继续。

2. 设计目标

1. 实现计算与消费的解耦
2. 支持背压控制（backpressure）- 当消费能力不足时暂停计算
3. 提供异步接口，充分利用异步运行时性能
4. 支持错误处理和任务取消
5. 提供监控指标支持

3. 核心组件

3.1 Sender（发送器）

负责发送计算结果到消费端。

pub struct AsyncSender<T> {
sender: Sender<Result<T>>,
}

主要方法：
- send(&self, item: T) -> Future<()>：异步发送数据项

3.2 Receiver（接收器）

负责接收并消费计算结果。

通常使用运行时提供的Receiver，如Tokio的Receiver<T>。

3.3 ExecutionContext（执行上下文）

管理整个异步计算过程的上下文。

pub struct ExecutionContext {
task_ctx: Arc<TaskContext>,
metrics: ExecutionPlanMetricsSet,
baseline_metrics: BaselineMetrics,
}

主要方法：
- output_with_sender<T, F>(desc: &'static str, output: F) -> SendableStream<T>：创建带发送器的异步流

3.4 WrappedSender（包装发送器）

对基础发送器的包装，提供额外功能如时间统计。

pub struct WrappedSender<T> {
sender: Sender<Result<T>>
}

4. 工作原理

4.1 基本流程

1. 使用output_with_sender创建一个异步流构建器
2. 构建器内部创建通道的发送端和接收端
3. 启动异步任务执行计算逻辑
4. 计算结果通过sender.send(item).await发送
5. 接收端消费数据

4.2 背压控制机制

当接收端消费速度跟不上发送端的发送速度时：
1. 通道缓冲区填满
2. sender.send().await调用会挂起，直到有空间
3. 这自然地暂停了计算任务，实现了背压控制

4.3 错误处理

- 发送错误通过Result<T>传递
- 任务panic通过catch_unwind捕获
- 支持任务取消机制



6. 核心实现

6.1 创建异步计算流

pub fn output_with_sender<T, Fut>(
&self,
desc: &'static str,
output: impl FnOnce(Arc<WrappedSender<T>>) -> Fut + Send + 'static,
) -> SendableStream<T>
where
T: Send + 'static,
Fut: Future<Output = Result<()>> + Send,
{
let mut stream_builder = RecordBatchReceiverStream::builder(self.output_schema(), 1);
let err_sender = stream_builder.tx().clone();
let wrapped_sender = WrappedSender::new(self.clone(), stream_builder.tx().clone());

    stream_builder.spawn(async move {
        let result = AssertUnwindSafe(async move {
            if let Err(err) = output(wrapped_sender).await {
                panic!("output_with_sender[{desc}]: output() returns error: {err}");
            }
        })
        .catch_unwind()
        .await
        .map(|_| Ok(()))
        .unwrap_or_else(|err| {
            let panic_message = panic_message::get_panic_message(&err)
                .unwrap_or("unknown error");
            Err(Error::Execution(panic_message))
        });

        if let Err(err) = result {
            err_sender.send(Err(err)).await.unwrap_or_default();
            // 处理任务取消等场景
        }
        Ok(())
    });
    stream_builder.build()
}

6.2 发送数据

impl<T> WrappedSender<T> {
pub async fn send(&self, item: T) {
if is_empty(&item) {
return;
}

        
        
        self.sender
            .send(Ok(item))
            .await
            .unwrap_or_else(|err| panic!("send error: {err}"));


7. 使用示例

7.1 定义计算任务

fn compute_task(sender: Arc<WrappedSender<ComputedData>>) -> impl Future<Output = Result<()>> {
async move {
for i in 0..1000 {
// 执行计算
let data = perform_computation(i);

            // 发送结果，如果消费者来不及处理，这里会自动等待
            sender.send(data).await;
        }
        Ok(())
    }
}

7.2 启动计算和消费

// 创建执行上下文
let exec_ctx = ExecutionContext::new(...);

// 启动计算任务
let mut stream = exec_ctx.output_with_sender("MyComputation", compute_task);

// 消费结果（例如写入数据库）
while let Some(result) = stream.next().await {
match result {
Ok(data) => {
// 写入数据库，这步较慢会自然地减缓计算任务
write_to_database(data).await?;
}
Err(e) => {
// 处理错误
return Err(e);
}
}
}
