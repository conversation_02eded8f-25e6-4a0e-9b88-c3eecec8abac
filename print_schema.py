import pandas as pd
import pyarrow.parquet as pq
import sys
# 直接读取单个parquet文件
file_path = sys.argv[1]

# 使用pyarrow直接读取文件
parquet_file = pq.ParquetFile(file_path)

# 获取arrow schema
arrow_schema = parquet_file.schema_arrow

print('Arrow Schema信息:')
for i, field in enumerate(arrow_schema):
    print(f'{i+1:3d}. {field.name:40s} {field.type}')

print(f'\n总字段数: {len(arrow_schema)}')

# 读取一些数据
table = parquet_file.read()
print(f'\n数据行数: {len(table)}')