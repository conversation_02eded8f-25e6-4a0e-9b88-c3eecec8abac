/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.262.b10-0.el7_8.x86_64/jre//bin/java -server -Xmx20480m -XX:+UseNUMA -Djava.io.tmpdir=/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/tmp -Dspark.network.timeout=1000000 -Dspark.history.ui.port=18082 -Dspark.driver.port=44200 -Dspark.ui.port=0 -Dspark.yarn.app.container.log.dir=/data4/hadoop/yarn/log/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003 -XX:OnOutOfMemoryError=kill %p org.apache.spark.executor.YarnCoarseGrainedExecutorBackend --driver-url spark://<EMAIL>:44200 --executor-id 2 --hostname gdp01.dev.guwave.com --cores 1 --app-id application_1754452240878_0013 --resourceProfileId 0 --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/__app__.jar --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/next-compute-engine-etl-1.4.0.jar --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/next-compute-engine-etl-1.4.0.properties --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/dataware-dw-test-item-3.4.5.jar --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/libnative-3.4.5.so --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/dataware-dw-test-item-3.4.5.properties --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/next-compute-engine-sql-1.4.0.jar --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/next-compute-engine-sql-1.4.0.properties --user-class-path file:/data2/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0013/container_e13_1754452240878_0013_01_000003/next-compute-engine-resident-1.4.2.properties


/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.262.b10-0.el7_8.x86_64/jre//bin/java -server -Xmx20480m -XX:+UseNUMA -classpath dataware-dw-test-item-3.4.5.jar:dataware-dw-test-item-3.4.5.properties com.guwave.onedata.dataware.dw.testItem.spark.task.impl.CpTestItemTask


 /usr/lib/jvm/java-1.8.0-openjdk-1.8.0.262.b10-0.el7_8.x86_64/jre//bin/java -server -Xmx20480m -Djava.io.tmpdir=/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/tmp -Dspark.network.timeout=1000000 -Dspark.history.ui.port=18082 -Dspark.driver.port=38741 -Dspark.ui.port=0 -Dspark.yarn.app.container.log.dir=/data4/hadoop/yarn/log/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002 -XX:OnOutOfMemoryError=kill %p org.apache.spark.executor.YarnCoarseGrainedExecutorBackend --driver-url spark://
/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.262.b10-0.el7_8.x86_64/jre//bin/java -server -Xmx20480m -XX:+UseNUMA -classpath dataware-dw-test-item-3.4.5.jar:./hadoop/client/shaded/*:/usr/hdp/*******-315/spark3/jars/*:<EMAIL>:38741 --executor-id 1 --hostname gdp01.dev.guwave.com --cores 5 --app-id application_1754452240878_0017 --resourceProfileId 0 --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/__app__.jar --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/next-compute-engine-etl-1.4.0.jar --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_17544522


/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.262.b10-0.el7_8.x86_64/jre//bin/java -server -Xmx20480m -XX:+UseNUMA -classpath dataware-dw-test-item-3.4.5.jar:./hadoop/client/shaded/*:/usr/hdp/*******-315/spark3/jars/*:da40878_0017_01_000002/next-compute-engine-etl-1.4.0.properties --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/dataware-dw-test-item-3.4.5.jar --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/libnative-3.4.5.so --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/dataware-dw-test-item-3.4.5.properties --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/next-compute-engine-sql-1.4.0.jar --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/next-compute-engine-sql-1.4.0.properties --user-class-path file:/data4/hadoop/yarn/local/usercache/glory/appcache/application_1754452240878_0017/container_e13_1754452240878_0017_01_000002/next-compute-engine-resident-1.4.2.properties


/home/<USER>/deploy/onedata/dataware/dataware-dw-test-item/native/libnative-{version}.so,/home/<USER>/deploy/onedata/dataware/dataware-dw-test-item/properties/dataware-dw-test-item-{version}.properties


