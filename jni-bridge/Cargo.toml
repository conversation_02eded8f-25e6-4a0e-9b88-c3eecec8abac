[package]
name = "jni-bridge"
version = "0.1.0"
edition = "2021"

[dependencies]
jni = { workspace = true }
log = "0.4.26"
once_cell = "1.20.3"
paste = "1.0.15"
panic-message = "0.3.0"
thiserror = { workspace = true }
regex = { workspace = true }
color-eyre = { workspace = true }
tracing-subscriber = { workspace = true, features = ["time", "local-time"] }
tracing = { workspace = true }
time = { workspace = true, features = ["macros"] }
